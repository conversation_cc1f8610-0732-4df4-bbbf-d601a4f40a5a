/*
财务软件专用打印样式
支持A4横向和纵向打印，优化财务凭证和报表打印效果
*/

/* 通用打印样式 */
@media print {
    /* 重置页面样式 */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    /* 隐藏不需要打印的元素 */
    .no-print,
    .navbar,
    .sidebar,
    .btn,
    .pagination,
    .breadcrumb,
    .alert,
    .modal,
    .tooltip,
    .popover {
        display: none !important;
    }
    
    /* 页面设置 */
    @page {
        margin: 15mm;
        size: A4;
    }
    
    /* A4横向打印 */
    @page landscape {
        size: A4 landscape;
        margin: 10mm 15mm;
    }
    
    /* 基础样式 */
    body {
        font-family: 'SimSun', '宋体', serif !important;
        font-size: 12px !important;
        line-height: 1.4 !important;
        color: #000 !important;
        background: white !important;
    }
    
    /* 标题样式 */
    .print-title {
        font-size: 18px !important;
        font-weight: bold !important;
        text-align: center !important;
        margin: 0 0 20px 0 !important;
        padding: 0 !important;
        border: none !important;
    }
    
    .print-subtitle {
        font-size: 14px !important;
        font-weight: bold !important;
        text-align: center !important;
        margin: 0 0 15px 0 !important;
    }
    
    /* 表格样式 */
    .print-table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin: 10px 0 !important;
        font-size: 10px !important;
    }
    
    .print-table th,
    .print-table td {
        border: 1px solid #000 !important;
        padding: 4px 6px !important;
        text-align: left !important;
        vertical-align: middle !important;
        background: white !important;
    }
    
    .print-table th {
        background: #f0f0f0 !important;
        font-weight: bold !important;
        text-align: center !important;
    }
    
    .print-table .amount {
        text-align: right !important;
        font-family: 'Arial', sans-serif !important;
    }
    
    .print-table .center {
        text-align: center !important;
    }
    
    /* 凭证专用样式 */
    .voucher-print {
        page: landscape;
    }
    
    .voucher-header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        margin-bottom: 15px !important;
        border-bottom: 1px solid #000 !important;
        padding-bottom: 10px !important;
    }
    
    .voucher-info {
        display: flex !important;
        gap: 30px !important;
        margin-bottom: 15px !important;
    }
    
    .voucher-info-item {
        display: flex !important;
        align-items: center !important;
        gap: 5px !important;
    }
    
    .voucher-details-table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin: 15px 0 !important;
    }
    
    .voucher-details-table th,
    .voucher-details-table td {
        border: 1px solid #000 !important;
        padding: 6px 8px !important;
        vertical-align: middle !important;
    }
    
    .voucher-details-table th {
        background: #f5f5f5 !important;
        font-weight: bold !important;
        text-align: center !important;
    }
    
    .voucher-signature {
        display: flex !important;
        justify-content: space-between !important;
        margin-top: 30px !important;
        padding-top: 20px !important;
    }
    
    .voucher-signature-item {
        display: flex !important;
        align-items: center !important;
        gap: 10px !important;
    }
    
    .signature-line {
        border-bottom: 1px solid #000 !important;
        width: 80px !important;
        height: 20px !important;
    }
    
    /* 报表专用样式 */
    .report-print {
        page: auto;
    }
    
    .report-header {
        text-align: center !important;
        margin-bottom: 20px !important;
    }
    
    .report-date {
        text-align: center !important;
        font-size: 12px !important;
        margin-bottom: 15px !important;
    }
    
    .balance-sheet-table {
        width: 100% !important;
        border-collapse: collapse !important;
    }
    
    .balance-sheet-table th,
    .balance-sheet-table td {
        border: 1px solid #000 !important;
        padding: 5px 8px !important;
        vertical-align: middle !important;
    }
    
    .balance-sheet-table th {
        background: #e8e8e8 !important;
        font-weight: bold !important;
        text-align: center !important;
    }
    
    .balance-sheet-table .total-row {
        background: #f0f0f0 !important;
        font-weight: bold !important;
    }
    
    /* 试算平衡表样式 */
    .trial-balance-table {
        width: 100% !important;
        border-collapse: collapse !important;
        font-size: 9px !important;
    }
    
    .trial-balance-table th,
    .trial-balance-table td {
        border: 1px solid #000 !important;
        padding: 3px 5px !important;
        vertical-align: middle !important;
    }
    
    .trial-balance-table th {
        background: #e8e8e8 !important;
        font-weight: bold !important;
        text-align: center !important;
    }
    
    /* 页脚信息 */
    .print-footer {
        position: fixed !important;
        bottom: 10mm !important;
        left: 0 !important;
        right: 0 !important;
        text-align: center !important;
        font-size: 10px !important;
        color: #666 !important;
    }
    
    /* 分页控制 */
    .page-break {
        page-break-before: always !important;
    }
    
    .no-page-break {
        page-break-inside: avoid !important;
    }
    
    /* 金额格式化 */
    .amount-cell {
        text-align: right !important;
        font-family: 'Arial', 'Times New Roman', monospace !important;
        white-space: nowrap !important;
    }
    
    /* 科目编码格式化 */
    .subject-code {
        font-family: 'Arial', 'Times New Roman', monospace !important;
        font-weight: bold !important;
    }
    
    /* 隐藏背景色和阴影 */
    .card,
    .table-striped tbody tr:nth-of-type(odd),
    .table-hover tbody tr:hover {
        background: white !important;
        box-shadow: none !important;
    }
    
    /* 强制显示边框 */
    .table,
    .table th,
    .table td {
        border: 1px solid #000 !important;
    }
}

/* 屏幕预览样式 */
.print-preview {
    background: white;
    padding: 20px;
    margin: 20px auto;
    max-width: 210mm;
    min-height: 297mm;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    font-family: 'SimSun', '宋体', serif;
    font-size: 12px;
    line-height: 1.4;
}

.print-preview.landscape {
    max-width: 297mm;
    min-height: 210mm;
}

/* 打印按钮样式 */
.print-controls {
    text-align: center;
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
}

.print-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    margin: 0 5px;
    font-size: 14px;
}

.print-btn:hover {
    background: #0056b3;
}

.print-btn.landscape {
    background: #28a745;
}

.print-btn.landscape:hover {
    background: #1e7e34;
}

/* 响应式打印 */
@media screen and (max-width: 768px) {
    .print-preview {
        max-width: 100%;
        margin: 10px;
        padding: 15px;
    }
    
    .print-controls {
        margin: 10px 0;
        padding: 10px;
    }
    
    .print-btn {
        display: block;
        width: 100%;
        margin: 5px 0;
    }
}
