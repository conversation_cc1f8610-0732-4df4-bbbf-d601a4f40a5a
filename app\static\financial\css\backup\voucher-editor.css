/* 专业记账凭证编辑器样式 */

.voucher-editor-container {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* 编辑器表格样式 */
.voucher-editor-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border: 2px solid #333;
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.voucher-editor-table th {
    background: #e9ecef;
    border: 1px solid #333;
    padding: 8px;
    text-align: center;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
}

.voucher-editor-table td {
    border: 1px solid #333;
    padding: 0;
    position: relative;
}

/* 可编辑单元格 */
.editable-cell {
    min-height: 35px;
    padding: 6px 8px;
    border: none;
    outline: none;
    width: 100%;
    background: transparent;
    font-family: inherit;
    font-size: inherit;
    resize: none;
    overflow: hidden;
}

.editable-cell:focus {
    background: #fff3cd;
    box-shadow: inset 0 0 0 2px #007bff;
}

/* 选中的行 */
.voucher-editor-table tr.selected {
    background: #e3f2fd;
}

.voucher-editor-table tr.active {
    background: #fff3cd;
}

/* 科目选择器 */
.subject-selector {
    position: relative;
    width: 100%;
}

.subject-input {
    width: 100%;
    padding: 6px 8px;
    border: none;
    outline: none;
    background: transparent;
    font-family: inherit;
    cursor: pointer;
}

.subject-input:focus {
    background: #fff3cd;
    box-shadow: inset 0 0 0 2px #007bff;
}

.subject-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ccc;
    border-top: none;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.subject-option {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.subject-option:hover,
.subject-option.highlighted {
    background: #007bff;
    color: white;
}

.subject-option .subject-code {
    font-weight: bold;
    color: #666;
}

.subject-option .subject-name {
    margin-left: 8px;
}

/* 金额输入框 */
.amount-cell {
    text-align: right;
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.amount-input {
    text-align: right;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    letter-spacing: 1px;
}

.amount-input.debit {
    color: #dc3545;
}

.amount-input.credit {
    color: #28a745;
}

/* 金额单位标识 */
.amount-units {
    font-size: 13px;
    color: #666;
    text-align: center;
    padding: 2px 0;
    border-bottom: 1px solid #ddd;
}

.amount-units span {
    display: inline-block;
    width: 12px;
    text-align: center;
}

/* 合计行 */
.total-row {
    background: #f8f9fa;
    font-weight: bold;
}

.total-row td {
    border-top: 2px solid #333;
}

.amount-words-row {
    background: #f8f9fa;
}

.amount-words-row td {
    padding: 8px;
    font-weight: bold;
    color: #495057;
}

/* 操作按钮 */
.row-actions {
    text-align: center;
    padding: 4px;
}

.row-actions .btn {
    margin: 0 2px;
    padding: 2px 6px;
    font-size: 12px;
}

/* 工具栏 */
.voucher-toolbar {
    background: white;
    padding: 10px;
    border: 1px solid #ddd;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
}

.voucher-toolbar .btn {
    margin-right: 8px;
    margin-bottom: 4px;
}

/* 状态指示器 */
.balance-indicator {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    margin-left: 10px;
}

.balance-indicator.balanced {
    background: #d4edda;
    color: #155724;
}

.balance-indicator.unbalanced {
    background: #f8d7da;
    color: #721c24;
}

/* 快捷键提示 */
.shortcut-hint {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    display: none;
}

.shortcut-hint.show {
    display: block;
}

/* 行号 */
.row-number {
    background: #e9ecef;
    text-align: center;
    font-weight: bold;
    color: #666;
    width: 40px;
    user-select: none;
}

/* 错误提示 */
.cell-error {
    border: 2px solid #dc3545 !important;
    background: #f8d7da !important;
}

.error-tooltip {
    position: absolute;
    top: 100%;
    left: 0;
    background: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1001;
    white-space: nowrap;
}

/* 自动完成 */
.autocomplete-container {
    position: relative;
}

.autocomplete-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ccc;
    max-height: 150px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.autocomplete-suggestion {
    padding: 6px 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.autocomplete-suggestion:hover,
.autocomplete-suggestion.selected {
    background: #007bff;
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .voucher-editor-table {
        font-size: 12px;
    }
    
    .editable-cell {
        padding: 4px 6px;
        min-height: 30px;
    }
    
    .voucher-toolbar .btn {
        padding: 4px 8px;
        font-size: 12px;
    }
}

/* 打印样式 */
@media print {
    .voucher-toolbar,
    .row-actions,
    .voucher-actions {
        display: none !important;
    }
    
    .voucher-editor-table {
        border: 2px solid black;
    }
    
    .voucher-editor-table th,
    .voucher-editor-table td {
        border: 1px solid black;
    }
    
    .editable-cell {
        border: none;
        background: transparent !important;
    }
}
