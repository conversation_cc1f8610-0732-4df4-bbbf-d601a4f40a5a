/**
 * 用友风格表格交互功能
 * 实现用友财务软件的表格交互特性：行选择、排序、筛选、快捷键操作等
 */

class UFTableInteractions {
    constructor(tableSelector, options = {}) {
        this.table = document.querySelector(tableSelector);
        this.options = {
            enableRowSelection: true,
            enableSorting: true,
            enableKeyboardNavigation: true,
            enableRowHover: true,
            enableDoubleClickEdit: true,
            ...options
        };
        
        this.selectedRows = new Set();
        this.currentRow = null;
        this.sortState = {};
        
        this.init();
    }
    
    init() {
        if (!this.table) {
            console.warn('Table not found:', this.tableSelector);
            return;
        }
        
        this.setupRowSelection();
        this.setupSorting();
        this.setupKeyboardNavigation();
        this.setupRowHover();
        this.setupDoubleClickEdit();
        this.setupContextMenu();
    }
    
    // 行选择功能
    setupRowSelection() {
        if (!this.options.enableRowSelection) return;
        
        // 全选功能
        const selectAllCheckbox = this.table.querySelector('th input[type="checkbox"]');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.selectAll(e.target.checked);
            });
        }
        
        // 单行选择
        const rowCheckboxes = this.table.querySelectorAll('tbody input[type="checkbox"]');
        rowCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.selectRow(e.target.closest('tr'), e.target.checked);
            });
        });
        
        // 点击行选择
        const rows = this.table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('click', (e) => {
                if (e.target.type !== 'checkbox' && !e.target.closest('button') && !e.target.closest('a')) {
                    this.toggleRowSelection(row);
                }
            });
        });
    }
    
    // 排序功能
    setupSorting() {
        if (!this.options.enableSorting) return;
        
        const headers = this.table.querySelectorAll('th[data-sortable]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                this.sortColumn(header);
            });
            
            // 添加排序图标
            if (!header.querySelector('.uf-sort-icon')) {
                const sortIcon = document.createElement('span');
                sortIcon.className = 'uf-sort-icon';
                sortIcon.innerHTML = '↕';
                header.appendChild(sortIcon);
            }
        });
    }
    
    // 键盘导航
    setupKeyboardNavigation() {
        if (!this.options.enableKeyboardNavigation) return;
        
        document.addEventListener('keydown', (e) => {
            if (!this.isTableFocused()) return;
            
            switch(e.key) {
                case 'ArrowUp':
                    e.preventDefault();
                    this.navigateUp();
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.navigateDown();
                    break;
                case ' ':
                    e.preventDefault();
                    if (this.currentRow) {
                        this.toggleRowSelection(this.currentRow);
                    }
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (this.currentRow) {
                        this.editRow(this.currentRow);
                    }
                    break;
                case 'Delete':
                    e.preventDefault();
                    this.deleteSelectedRows();
                    break;
                case 'a':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        this.selectAll(true);
                    }
                    break;
                case 'Escape':
                    this.clearSelection();
                    break;
            }
        });
    }
    
    // 行悬停效果
    setupRowHover() {
        if (!this.options.enableRowHover) return;
        
        const rows = this.table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', () => {
                if (!row.classList.contains('selected')) {
                    row.classList.add('uf-row-hover');
                }
            });
            
            row.addEventListener('mouseleave', () => {
                row.classList.remove('uf-row-hover');
            });
        });
    }
    
    // 双击编辑
    setupDoubleClickEdit() {
        if (!this.options.enableDoubleClickEdit) return;
        
        const rows = this.table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('dblclick', () => {
                this.editRow(row);
            });
        });
    }
    
    // 右键菜单
    setupContextMenu() {
        const rows = this.table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                this.showContextMenu(e, row);
            });
        });
    }
    
    // 选择所有行
    selectAll(checked) {
        const rows = this.table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            this.selectRow(row, checked);
        });
        
        this.updateSelectAllCheckbox();
        this.onSelectionChange();
    }
    
    // 选择单行
    selectRow(row, checked) {
        const checkbox = row.querySelector('input[type="checkbox"]');
        if (checkbox) {
            checkbox.checked = checked;
        }
        
        if (checked) {
            row.classList.add('selected');
            this.selectedRows.add(row);
        } else {
            row.classList.remove('selected');
            this.selectedRows.delete(row);
        }
    }
    
    // 切换行选择状态
    toggleRowSelection(row) {
        const isSelected = this.selectedRows.has(row);
        this.selectRow(row, !isSelected);
        this.updateSelectAllCheckbox();
        this.onSelectionChange();
    }
    
    // 更新全选复选框状态
    updateSelectAllCheckbox() {
        const selectAllCheckbox = this.table.querySelector('th input[type="checkbox"]');
        if (!selectAllCheckbox) return;
        
        const totalRows = this.table.querySelectorAll('tbody tr').length;
        const selectedCount = this.selectedRows.size;
        
        selectAllCheckbox.checked = selectedCount === totalRows && totalRows > 0;
        selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < totalRows;
    }
    
    // 排序列
    sortColumn(header) {
        const column = header.dataset.sortable;
        const currentSort = this.sortState[column] || 'none';
        
        // 重置其他列的排序状态
        Object.keys(this.sortState).forEach(key => {
            if (key !== column) {
                this.sortState[key] = 'none';
                const otherHeader = this.table.querySelector(`th[data-sortable="${key}"]`);
                if (otherHeader) {
                    const icon = otherHeader.querySelector('.uf-sort-icon');
                    if (icon) icon.innerHTML = '↕';
                }
            }
        });
        
        // 切换当前列的排序状态
        let newSort;
        switch(currentSort) {
            case 'none':
                newSort = 'asc';
                break;
            case 'asc':
                newSort = 'desc';
                break;
            case 'desc':
                newSort = 'none';
                break;
        }
        
        this.sortState[column] = newSort;
        
        // 更新排序图标
        const icon = header.querySelector('.uf-sort-icon');
        if (icon) {
            switch(newSort) {
                case 'asc':
                    icon.innerHTML = '↑';
                    break;
                case 'desc':
                    icon.innerHTML = '↓';
                    break;
                case 'none':
                    icon.innerHTML = '↕';
                    break;
            }
        }
        
        // 执行排序
        if (newSort !== 'none') {
            this.performSort(column, newSort);
        } else {
            this.resetSort();
        }
    }
    
    // 执行排序
    performSort(column, direction) {
        const tbody = this.table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = this.getColumnIndex(column);
        
        rows.sort((a, b) => {
            const aValue = this.getCellValue(a, columnIndex);
            const bValue = this.getCellValue(b, columnIndex);
            
            let result = 0;
            if (aValue < bValue) result = -1;
            else if (aValue > bValue) result = 1;
            
            return direction === 'desc' ? -result : result;
        });
        
        // 重新排列行
        rows.forEach(row => tbody.appendChild(row));
    }
    
    // 获取列索引
    getColumnIndex(column) {
        const headers = this.table.querySelectorAll('th');
        for (let i = 0; i < headers.length; i++) {
            if (headers[i].dataset.sortable === column) {
                return i;
            }
        }
        return 0;
    }
    
    // 获取单元格值
    getCellValue(row, columnIndex) {
        const cell = row.cells[columnIndex];
        if (!cell) return '';
        
        // 尝试获取数值
        const numValue = parseFloat(cell.textContent.replace(/[^\d.-]/g, ''));
        if (!isNaN(numValue)) return numValue;
        
        // 返回文本值
        return cell.textContent.trim().toLowerCase();
    }
    
    // 键盘导航
    navigateUp() {
        const rows = Array.from(this.table.querySelectorAll('tbody tr'));
        const currentIndex = this.currentRow ? rows.indexOf(this.currentRow) : 0;
        const newIndex = Math.max(0, currentIndex - 1);
        this.setCurrentRow(rows[newIndex]);
    }
    
    navigateDown() {
        const rows = Array.from(this.table.querySelectorAll('tbody tr'));
        const currentIndex = this.currentRow ? rows.indexOf(this.currentRow) : -1;
        const newIndex = Math.min(rows.length - 1, currentIndex + 1);
        this.setCurrentRow(rows[newIndex]);
    }
    
    setCurrentRow(row) {
        if (this.currentRow) {
            this.currentRow.classList.remove('uf-row-current');
        }
        
        this.currentRow = row;
        if (row) {
            row.classList.add('uf-row-current');
            row.scrollIntoView({ block: 'nearest' });
        }
    }
    
    // 检查表格是否获得焦点
    isTableFocused() {
        return this.table.contains(document.activeElement) || 
               document.activeElement === document.body;
    }
    
    // 编辑行
    editRow(row) {
        const editBtn = row.querySelector('.uf-action-edit');
        if (editBtn) {
            editBtn.click();
        } else {
            // 触发自定义编辑事件
            this.onRowEdit(row);
        }
    }
    
    // 删除选中行
    deleteSelectedRows() {
        if (this.selectedRows.size === 0) return;
        
        const selectedIds = Array.from(this.selectedRows).map(row => {
            const checkbox = row.querySelector('input[type="checkbox"]');
            return checkbox ? checkbox.value : null;
        }).filter(id => id);
        
        if (selectedIds.length > 0) {
            this.onRowsDelete(selectedIds);
        }
    }
    
    // 清除选择
    clearSelection() {
        this.selectAll(false);
    }
    
    // 显示右键菜单
    showContextMenu(event, row) {
        // 创建右键菜单
        const menu = document.createElement('div');
        menu.className = 'uf-context-menu';
        menu.style.position = 'fixed';
        menu.style.left = event.clientX + 'px';
        menu.style.top = event.clientY + 'px';
        menu.style.zIndex = '9999';
        
        const menuItems = [
            { text: '编辑', action: () => this.editRow(row) },
            { text: '复制', action: () => this.onRowCopy(row) },
            { text: '删除', action: () => this.onRowDelete(row) },
            { text: '查看详情', action: () => this.onRowView(row) }
        ];
        
        menuItems.forEach(item => {
            const menuItem = document.createElement('div');
            menuItem.className = 'uf-context-menu-item';
            menuItem.textContent = item.text;
            menuItem.addEventListener('click', () => {
                item.action();
                document.body.removeChild(menu);
            });
            menu.appendChild(menuItem);
        });
        
        document.body.appendChild(menu);
        
        // 点击其他地方关闭菜单
        const closeMenu = (e) => {
            if (!menu.contains(e.target)) {
                document.body.removeChild(menu);
                document.removeEventListener('click', closeMenu);
            }
        };
        
        setTimeout(() => {
            document.addEventListener('click', closeMenu);
        }, 0);
    }
    
    // 事件回调
    onSelectionChange() {
        // 可以被子类重写
        console.log('Selection changed:', this.selectedRows.size);
    }
    
    onRowEdit(row) {
        // 可以被子类重写
        console.log('Edit row:', row);
    }
    
    onRowsDelete(ids) {
        // 可以被子类重写
        console.log('Delete rows:', ids);
    }
    
    onRowCopy(row) {
        // 可以被子类重写
        console.log('Copy row:', row);
    }
    
    onRowDelete(row) {
        // 可以被子类重写
        console.log('Delete row:', row);
    }
    
    onRowView(row) {
        // 可以被子类重写
        console.log('View row:', row);
    }
    
    // 获取选中的行数据
    getSelectedData() {
        return Array.from(this.selectedRows).map(row => {
            const checkbox = row.querySelector('input[type="checkbox"]');
            return {
                id: checkbox ? checkbox.value : null,
                row: row,
                data: this.extractRowData(row)
            };
        });
    }
    
    // 提取行数据
    extractRowData(row) {
        const cells = row.querySelectorAll('td');
        const data = {};
        
        cells.forEach((cell, index) => {
            const header = this.table.querySelector(`th:nth-child(${index + 1})`);
            const key = header ? (header.dataset.field || `col_${index}`) : `col_${index}`;
            data[key] = cell.textContent.trim();
        });
        
        return data;
    }
}

// 导出类
window.UFTableInteractions = UFTableInteractions;
