/**
 * 用友风格用户体验优化脚本
 * 提供用友财务软件的用户体验特性：快捷键、提示、动画效果等
 */

class UFUserExperience {
    constructor() {
        this.init();
    }
    
    init() {
        this.setupKeyboardShortcuts();
        this.setupTooltips();
        this.setupLoadingEffects();
        this.setupFormValidation();
        this.setupAutoSave();
        this.setupContextHelp();
        this.setupAnimations();
    }
    
    // 设置键盘快捷键
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl + S: 保存
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.triggerSave();
            }
            
            // Ctrl + N: 新建
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                this.triggerNew();
            }
            
            // Ctrl + P: 打印
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                this.triggerPrint();
            }
            
            // F1: 帮助
            if (e.key === 'F1') {
                e.preventDefault();
                this.showHelp();
            }
            
            // F5: 刷新
            if (e.key === 'F5') {
                e.preventDefault();
                this.refreshPage();
            }
            
            // Escape: 取消/关闭
            if (e.key === 'Escape') {
                this.handleEscape();
            }
        });
    }
    
    // 设置工具提示
    setupTooltips() {
        // 为所有带有title属性的元素添加用友风格提示
        const elementsWithTitle = document.querySelectorAll('[title]');
        elementsWithTitle.forEach(element => {
            this.createUFTooltip(element);
        });
        
        // 为操作按钮添加快捷键提示
        this.addShortcutTooltips();
    }
    
    // 创建用友风格提示框
    createUFTooltip(element) {
        const title = element.getAttribute('title');
        if (!title) return;
        
        element.removeAttribute('title'); // 移除默认提示
        
        let tooltip = null;
        
        element.addEventListener('mouseenter', (e) => {
            tooltip = document.createElement('div');
            tooltip.className = 'uf-tooltip';
            tooltip.textContent = title;
            document.body.appendChild(tooltip);
            
            const rect = element.getBoundingClientRect();
            tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
            tooltip.style.top = rect.bottom + 5 + 'px';
            
            setTimeout(() => {
                if (tooltip) tooltip.classList.add('show');
            }, 100);
        });
        
        element.addEventListener('mouseleave', () => {
            if (tooltip) {
                tooltip.classList.remove('show');
                setTimeout(() => {
                    if (tooltip && tooltip.parentNode) {
                        tooltip.parentNode.removeChild(tooltip);
                    }
                }, 200);
            }
        });
    }
    
    // 添加快捷键提示
    addShortcutTooltips() {
        const shortcuts = {
            '.uf-btn[onclick*="save"]': 'Ctrl+S',
            '.uf-btn[href*="create"]': 'Ctrl+N',
            '.uf-btn[onclick*="print"]': 'Ctrl+P',
            '.uf-btn[onclick*="help"]': 'F1'
        };
        
        Object.entries(shortcuts).forEach(([selector, shortcut]) => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                const originalTitle = element.getAttribute('title') || element.textContent.trim();
                element.setAttribute('title', `${originalTitle} (${shortcut})`);
                this.createUFTooltip(element);
            });
        });
    }
    
    // 设置加载效果
    setupLoadingEffects() {
        // 为表单提交添加加载效果
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                this.showLoadingOverlay('正在处理...');
            });
        });
        
        // 为AJAX请求添加加载效果
        this.interceptAjaxRequests();
    }
    
    // 显示加载遮罩
    showLoadingOverlay(message = '加载中...') {
        const overlay = document.createElement('div');
        overlay.className = 'uf-loading-overlay';
        overlay.innerHTML = `
            <div class="uf-loading-content">
                <div class="uf-loading-spinner"></div>
                <div class="uf-loading-text">${message}</div>
            </div>
        `;
        document.body.appendChild(overlay);
        
        setTimeout(() => overlay.classList.add('show'), 10);
        
        return overlay;
    }
    
    // 隐藏加载遮罩
    hideLoadingOverlay() {
        const overlay = document.querySelector('.uf-loading-overlay');
        if (overlay) {
            overlay.classList.remove('show');
            setTimeout(() => {
                if (overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
            }, 300);
        }
    }
    
    // 拦截AJAX请求
    interceptAjaxRequests() {
        const originalFetch = window.fetch;
        window.fetch = (...args) => {
            const overlay = this.showLoadingOverlay();
            return originalFetch(...args).finally(() => {
                setTimeout(() => this.hideLoadingOverlay(), 500);
            });
        };
    }
    
    // 设置表单验证
    setupFormValidation() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            this.enhanceFormValidation(form);
        });
    }
    
    // 增强表单验证
    enhanceFormValidation(form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
            
            input.addEventListener('input', () => {
                this.clearFieldError(input);
            });
        });
    }
    
    // 验证字段
    validateField(field) {
        const value = field.value.trim();
        const isRequired = field.hasAttribute('required');
        
        if (isRequired && !value) {
            this.showFieldError(field, '此字段为必填项');
            return false;
        }
        
        // 数字验证
        if (field.type === 'number' && value && isNaN(value)) {
            this.showFieldError(field, '请输入有效的数字');
            return false;
        }
        
        // 金额验证
        if (field.classList.contains('amount-input') && value) {
            if (!/^\d+(\.\d{1,2})?$/.test(value)) {
                this.showFieldError(field, '请输入有效的金额格式');
                return false;
            }
        }
        
        this.clearFieldError(field);
        return true;
    }
    
    // 显示字段错误
    showFieldError(field, message) {
        this.clearFieldError(field);
        
        field.classList.add('uf-field-error');
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'uf-field-error-message';
        errorDiv.textContent = message;
        
        field.parentNode.insertBefore(errorDiv, field.nextSibling);
    }
    
    // 清除字段错误
    clearFieldError(field) {
        field.classList.remove('uf-field-error');
        
        const errorMessage = field.parentNode.querySelector('.uf-field-error-message');
        if (errorMessage) {
            errorMessage.parentNode.removeChild(errorMessage);
        }
    }
    
    // 设置自动保存
    setupAutoSave() {
        const autoSaveFields = document.querySelectorAll('[data-auto-save]');
        autoSaveFields.forEach(field => {
            let saveTimeout;
            field.addEventListener('input', () => {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => {
                    this.autoSave(field);
                }, 2000);
            });
        });
    }
    
    // 自动保存
    autoSave(field) {
        const form = field.closest('form');
        if (form) {
            const formData = new FormData(form);
            // 这里可以实现自动保存逻辑
            this.showNotification('已自动保存', 'success');
        }
    }
    
    // 设置上下文帮助
    setupContextHelp() {
        const helpButtons = document.querySelectorAll('.uf-help-btn');
        helpButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const helpTopic = button.getAttribute('data-help-topic');
                this.showContextHelp(helpTopic);
            });
        });
    }
    
    // 显示上下文帮助
    showContextHelp(topic) {
        const helpContent = this.getHelpContent(topic);
        this.showModal('帮助', helpContent);
    }
    
    // 获取帮助内容
    getHelpContent(topic) {
        const helpTopics = {
            'voucher-create': '创建凭证：填写凭证信息，确保借贷平衡...',
            'voucher-review': '审核凭证：检查凭证内容，确认无误后审核通过...',
            'keyboard-shortcuts': '快捷键：Ctrl+S保存，Ctrl+N新建，Ctrl+P打印...'
        };
        
        return helpTopics[topic] || '暂无帮助信息';
    }
    
    // 设置动画效果
    setupAnimations() {
        // 页面加载动画
        this.animatePageLoad();
        
        // 表格行动画
        this.animateTableRows();
        
        // 按钮点击动画
        this.animateButtonClicks();
    }
    
    // 页面加载动画
    animatePageLoad() {
        const elements = document.querySelectorAll('.voucher-edit-container, .uf-voucher-list-table');
        elements.forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                element.style.transition = 'all 0.3s ease';
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }
    
    // 表格行动画
    animateTableRows() {
        const rows = document.querySelectorAll('.uf-voucher-list-table tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', () => {
                row.style.transform = 'scale(1.01)';
                row.style.transition = 'transform 0.2s ease';
            });
            
            row.addEventListener('mouseleave', () => {
                row.style.transform = 'scale(1)';
            });
        });
    }
    
    // 按钮点击动画
    animateButtonClicks() {
        const buttons = document.querySelectorAll('.uf-btn, .uf-action-btn');
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                const ripple = document.createElement('span');
                ripple.className = 'uf-ripple';
                
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                
                button.appendChild(ripple);
                
                setTimeout(() => {
                    if (ripple.parentNode) {
                        ripple.parentNode.removeChild(ripple);
                    }
                }, 600);
            });
        });
    }
    
    // 显示通知
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `uf-notification uf-notification-${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => notification.classList.add('show'), 10);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    // 显示模态框
    showModal(title, content) {
        const modal = document.createElement('div');
        modal.className = 'uf-modal-overlay';
        modal.innerHTML = `
            <div class="uf-modal">
                <div class="uf-modal-header">
                    <h5>${title}</h5>
                    <button class="uf-modal-close">&times;</button>
                </div>
                <div class="uf-modal-body">${content}</div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        setTimeout(() => modal.classList.add('show'), 10);
        
        // 关闭事件
        modal.querySelector('.uf-modal-close').addEventListener('click', () => {
            this.closeModal(modal);
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal(modal);
            }
        });
    }
    
    // 关闭模态框
    closeModal(modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 300);
    }
    
    // 快捷键处理方法
    triggerSave() {
        const saveBtn = document.querySelector('[onclick*="save"], .btn-primary[type="submit"]');
        if (saveBtn) saveBtn.click();
    }
    
    triggerNew() {
        const newBtn = document.querySelector('[href*="create"], [onclick*="create"]');
        if (newBtn) newBtn.click();
    }
    
    triggerPrint() {
        const printBtn = document.querySelector('[onclick*="print"], [href*="print"]');
        if (printBtn) {
            printBtn.click();
        } else {
            window.print();
        }
    }
    
    showHelp() {
        this.showContextHelp('keyboard-shortcuts');
    }
    
    refreshPage() {
        window.location.reload();
    }
    
    handleEscape() {
        // 关闭模态框
        const modal = document.querySelector('.uf-modal-overlay.show');
        if (modal) {
            this.closeModal(modal);
            return;
        }
        
        // 清除选择
        const tableInteractions = window.ufTableInteractions;
        if (tableInteractions) {
            tableInteractions.clearSelection();
        }
    }
}

// 初始化用友用户体验
document.addEventListener('DOMContentLoaded', () => {
    window.ufUserExperience = new UFUserExperience();
});

// 导出类
window.UFUserExperience = UFUserExperience;
