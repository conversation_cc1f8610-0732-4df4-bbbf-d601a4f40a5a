{% extends "financial/base.html" %}

{% block page_title %}
{% if subject %}编辑会计科目{% else %}新增会计科目{% endif %}
{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.accounting_subjects_index') }}">会计科目管理</a></span>
<span class="uf-breadcrumb-item active">
    {% if subject %}编辑科目{% else %}新增科目{% endif %}
</span>
{% endblock %}

{% block page_actions %}
<a href="{{ url_for('financial.accounting_subjects_index') }}" class="uf-btn">
    <i class="fas fa-arrow-left uf-icon"></i> 返回列表
</a>
{% endblock %}

{% block financial_content %}
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-{% if subject %}edit{% else %}plus{% endif %} uf-icon"></i>
        {% if subject %}编辑会计科目{% else %}新增会计科目{% endif %}
    </div>
    <div class="uf-card-body">
            <form method="POST">
                {{ form.hidden_tag() }}

                <!-- 用友风格上级科目选择 - 优化对齐布局 -->
                <div style="display: grid; grid-template-columns: 100px 1fr; gap: 16px; align-items: start; margin-bottom: 20px;">
                    <label class="uf-form-label" style="padding-top: 8px; text-align: right; white-space: nowrap;">
                        <i class="fas fa-sitemap uf-icon"></i> 上级科目：
                    </label>
                    <div>
                        <!-- 创建规则说明 -->
                        <div style="background: #e6f2ff; border: 1px solid #b3d9ff; border-radius: 2px; padding: 10px; margin-bottom: 12px; font-size: 13px;">
                            <i class="fas fa-lightbulb uf-icon" style="color: var(--uf-primary);"></i>
                            <strong>创建规则：</strong>选择上级科目可以在现有科目下创建子科目，不选则创建独立的一级科目
                        </div>

                        <!-- 科目选择器和模式提示一行显示 -->
                        <div style="display: flex; gap: 12px; align-items: start; margin-bottom: 12px;">
                            <!-- 科目选择器 -->
                            <div style="flex: 2; border: 1px solid var(--uf-border); border-radius: 2px; background: white; padding: 8px;">
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <input type="text" id="parent-subject-display"
                                           placeholder="点击选择上级科目..."
                                           readonly
                                           class="uf-form-control"
                                           style="flex: 1; background: #f8f9fa; cursor: pointer;"
                                           onclick="openParentSubjectSelector()">
                                    <button type="button" class="uf-btn uf-btn-primary uf-btn-sm" onclick="openParentSubjectSelector()">
                                        <i class="fas fa-search uf-icon"></i> 选择科目
                                    </button>
                                    <button type="button" class="uf-btn uf-btn-sm" onclick="clearParentSelection()" id="clear-parent-btn" style="display: none;">
                                        <i class="fas fa-times uf-icon"></i> 清除
                                    </button>
                                </div>
                            </div>

                            <!-- 当前模式提示 -->
                            <div id="create-mode-tip" style="flex: 1;">
                                <div style="background: #e2e3e5; border: 1px solid #d6d8db; border-radius: 2px; padding: 10px; font-size: 13px; height: 100%; display: flex; align-items: center;">
                                    <div>
                                        <i class="fas fa-plus-circle uf-icon" style="color: var(--uf-secondary);"></i>
                                        <strong>当前模式：</strong>创建独立的一级科目
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{ form.parent_id(style="display: none;") }}

                        <!-- 已选择上级科目显示 -->
                        <div id="selected-parent" style="display: none; margin-bottom: 12px;">
                            <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 2px; padding: 10px; font-size: 13px;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <strong><i class="fas fa-check-circle uf-icon" style="color: var(--uf-success);"></i> 已选择上级科目：</strong>
                                        <span id="selected-parent-text" style="margin-left: 8px; font-weight: 600;"></span>
                                    </div>
                                    <button type="button" class="uf-btn uf-btn-sm" onclick="clearParentSelection()">
                                        <i class="fas fa-times uf-icon"></i> 取消选择
                                    </button>
                                </div>
                            </div>
                        </div>

                        <small style="color: #666; font-size: 13px; margin-top: 8px; display: block;">
                            （可选）选择上级科目可以在现有科目下创建子科目，不选则创建独立的一级科目
                        </small>
                    </div>
                </div>

                <!-- 用友风格输入字段 - 优化对齐布局 -->
                <div style="display: grid; grid-template-columns: 100px 1fr 100px 1fr; gap: 16px; align-items: start; margin-bottom: 20px;">
                    <!-- 科目编码 -->
                    <label class="uf-form-label" style="padding-top: 8px; text-align: right; white-space: nowrap;">
                        {{ form.code.label.text }}：
                    </label>
                    <div>
                        {{ form.code(class="uf-form-control" + (" uf-error" if form.code.errors else ""), style="width: 100%;") }}
                        {% if form.code.errors %}
                        <div style="color: var(--uf-danger); font-size: 13px; margin-top: 4px;">
                            {% for error in form.code.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small style="color: #666; font-size: 13px; margin-top: 4px; display: block;">
                            科目编码用于唯一标识会计科目
                        </small>
                    </div>

                    <!-- 科目名称 -->
                    <label class="uf-form-label" style="padding-top: 8px; text-align: right; white-space: nowrap;">
                        {{ form.name.label.text }}：
                    </label>
                    <div>
                        {{ form.name(class="uf-form-control" + (" uf-error" if form.name.errors else ""), style="width: 100%;") }}
                        {% if form.name.errors %}
                        <div style="color: var(--uf-danger); font-size: 13px; margin-top: 4px;">
                            {% for error in form.name.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 100px 1fr 100px 1fr; gap: 16px; align-items: start; margin-bottom: 20px;">
                    <!-- 科目类型 -->
                    <label class="uf-form-label" style="padding-top: 8px; text-align: right; white-space: nowrap;">
                        {{ form.subject_type.label.text }}：
                    </label>
                    <div>
                        {{ form.subject_type(class="uf-form-control" + (" uf-error" if form.subject_type.errors else ""), id="subject_type", style="width: 100%;") }}
                        {% if form.subject_type.errors %}
                        <div style="color: var(--uf-danger); font-size: 13px; margin-top: 4px;">
                            {% for error in form.subject_type.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>

                    <!-- 余额方向 -->
                    <label class="uf-form-label" style="padding-top: 8px; text-align: right; white-space: nowrap;">
                        {{ form.balance_direction.label.text }}：
                    </label>
                    <div>
                        {{ form.balance_direction(class="uf-form-control" + (" uf-error" if form.balance_direction.errors else ""), id="balance_direction", style="width: 100%;") }}
                        {% if form.balance_direction.errors %}
                        <div style="color: var(--uf-danger); font-size: 13px; margin-top: 4px;">
                            {% for error in form.balance_direction.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small style="color: #666; font-size: 13px; margin-top: 4px; display: block;" id="balance_direction_help">
                            资产类科目余额方向为借方，用于核算学校食堂拥有的各种资产
                        </small>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 100px 1fr; gap: 16px; align-items: start; margin-bottom: 20px;">
                    <!-- 科目说明 -->
                    <label class="uf-form-label" style="padding-top: 8px; text-align: right; white-space: nowrap;">
                        {{ form.description.label.text }}：
                    </label>
                    <div>
                        {{ form.description(class="uf-form-control" + (" uf-error" if form.description.errors else ""), rows="3", style="width: 100%;") }}
                        {% if form.description.errors %}
                        <div style="color: var(--uf-danger); font-size: 13px; margin-top: 4px;">
                            {% for error in form.description.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small style="color: #666; font-size: 13px; margin-top: 4px; display: block;">
                            可选，用于说明科目的用途和核算内容
                        </small>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 24px;">
                    {{ form.submit(class="uf-btn uf-btn-primary", style="padding: 8px 24px; margin-right: 12px;") }}
                    <a href="{{ url_for('financial.accounting_subjects_index') }}" class="uf-btn" style="padding: 8px 24px;">取消</a>
                </div>
            </form>
    </div>
</div>

<!-- 用友风格使用指南 -->
<div class="uf-card" style="margin-top: 10px;">
    <div class="uf-card-header">
        <i class="fas fa-question-circle uf-icon"></i> 创建会计科目指南
    </div>
    <div class="uf-card-body">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h6 style="color: var(--uf-primary); margin-bottom: 12px; font-size: 13px;">
                        <i class="fas fa-plus-circle uf-icon"></i> 两种创建方式
                    </h6>
                    <div style="margin-bottom: 16px;">
                        <div style="border-left: 4px solid var(--uf-primary); padding-left: 12px;">
                            <strong style="font-size: 12px;">1. 创建子科目</strong>
                            <p style="font-size: 13px; color: #666; margin: 4px 0;">选择上级科目 → 在现有科目下创建子科目</p>
                            <ul style="font-size: 13px; margin: 0; padding-left: 16px;">
                                <li>自动继承上级科目的类型和余额方向</li>
                                <li>自动生成子科目编码</li>
                                <li>适合精细化管理</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin-bottom: 16px;">
                        <div style="border-left: 4px solid var(--uf-success); padding-left: 12px;">
                            <strong style="font-size: 12px;">2. 创建独立科目</strong>
                            <p style="font-size: 13px; color: #666; margin: 4px 0;">不选上级科目 → 创建独立的一级科目</p>
                            <ul style="font-size: 13px; margin: 0; padding-left: 16px;">
                                <li>需要手动选择科目类型</li>
                                <li>系统自动设置余额方向</li>
                                <li>适合全新业务</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div>
                    <h6 style="color: var(--uf-warning); margin-bottom: 12px; font-size: 13px;">
                        <i class="fas fa-lightbulb uf-icon"></i> 推荐扩展示例
                    </h6>
                    <div style="font-size: 13px;">
                        <strong>📊 主营业务收入 → 细分收入：</strong>
                        <ul style="margin: 4px 0 12px 16px;">
                            <li>学生早餐收入、学生午餐收入</li>
                            <li>教师餐费收入、外来人员餐费</li>
                        </ul>

                        <strong>📦 库存商品 → 食材分类：</strong>
                        <ul style="margin: 4px 0 12px 16px;">
                            <li>米面粮油、新鲜蔬菜</li>
                            <li>肉禽蛋类、调料用品</li>
                        </ul>

                        <strong>💰 管理费用 → 费用细分：</strong>
                        <ul style="margin: 4px 0 12px 16px;">
                            <li>水电燃气费、厨师工资</li>
                            <li>设备维护费、清洁用品费</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div style="background: #e6f2ff; border: 1px solid #b3d9ff; border-radius: 2px; padding: 12px; margin-top: 16px;">
                <i class="fas fa-magic uf-icon"></i>
                <strong>智能功能：</strong>
                系统会根据您的选择自动生成科目编码、设置余额方向，让创建科目变得简单快捷！
            </div>
        </div>
    </div>
</div>

<!-- 用友风格科目选择器模态框 -->
<div class="modal fade" id="parentSubjectModal" tabindex="-1" role="dialog" aria-labelledby="parentSubjectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 900px;">
        <div class="modal-content" style="border: 1px solid var(--uf-border); border-radius: 2px;">
            <div class="modal-header" style="background: var(--uf-header-bg); border-bottom: 1px solid var(--uf-border); padding: 12px 16px;">
                <h5 class="modal-title" id="parentSubjectModalLabel" style="font-size: 14px; font-weight: 600; color: var(--uf-primary);">
                    <i class="fas fa-sitemap uf-icon"></i> 选择上级科目
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="font-size: 18px; color: #666;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="padding: 0;">
                <!-- 用友风格搜索栏 -->
                <div style="background: #f8f9fa; border-bottom: 1px solid var(--uf-border); padding: 12px;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <input type="text" id="subject-search" placeholder="输入科目编码或名称进行搜索..."
                               style="flex: 1; font-size: 13px; padding: 6px 8px; border: 1px solid #c0c0c0; border-radius: 1px;">
                        <button type="button" class="uf-btn uf-btn-primary uf-btn-sm" onclick="searchSubjects()">
                            <i class="fas fa-search uf-icon"></i> 搜索
                        </button>
                        <button type="button" class="uf-btn uf-btn-sm" onclick="clearSearch()">
                            <i class="fas fa-undo uf-icon"></i> 重置
                        </button>
                    </div>
                </div>

                <!-- 用友风格科目树 -->
                <div style="height: 450px; overflow-y: auto; padding: 12px;">
                    <div id="modal-subject-tree">
                        <div style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 12px;"></i>
                            <p>加载科目数据中...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="background: #f8f9fa; border-top: 1px solid var(--uf-border); padding: 12px 16px;">
                <div style="flex: 1; font-size: 13px; color: #666;">
                    <i class="fas fa-info-circle uf-icon"></i>
                    点击科目名称选择，点击展开图标查看子科目
                </div>
                <button type="button" class="uf-btn" data-dismiss="modal">
                    <i class="fas fa-times uf-icon"></i> 取消
                </button>
                <button type="button" class="uf-btn uf-btn-primary" onclick="confirmParentSelection()">
                    <i class="fas fa-check uf-icon"></i> 确定选择
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_css %}
<style>
/* 用友风格科目选择器样式 */
.uf-subject-selector {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
}

/* 用友风格模态框样式 */
.modal-content {
    border: 1px solid var(--uf-border);
    border-radius: 2px;
    box-shadow: 0 4px 12px rgba(0,102,204,0.2);
}

.modal-header {
    background: var(--uf-header-bg);
    border-bottom: 1px solid var(--uf-border);
}

.modal-footer {
    background: #f8f9fa;
    border-top: 1px solid var(--uf-border);
}

/* 用友风格科目树样式 */
.uf-subject-tree {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
}

.uf-subject-tree .subject-group {
    margin-bottom: 12px;
}

.uf-subject-tree .subject-group-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--uf-primary);
    border-bottom: 1px solid var(--uf-border);
    padding: 6px 0;
    cursor: pointer;
    user-select: none;
    transition: var(--uf-transition);
}

.uf-subject-tree .subject-group-title:hover {
    color: var(--uf-primary-dark);
}

.uf-subject-tree .subject-item {
    margin-bottom: 2px;
}

.uf-subject-tree .subject-item-content {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 2px;
    cursor: pointer;
    transition: var(--uf-transition);
    font-size: var(--uf-font-size);
}

.uf-subject-tree .subject-item-content:hover {
    background: var(--uf-row-hover);
}

.uf-subject-tree .subject-item-content.selected {
    background: var(--uf-primary);
    color: white;
}

.uf-subject-tree .subject-item-content.selected .uf-code {
    background: rgba(255,255,255,0.2);
    color: white;
    border-color: rgba(255,255,255,0.3);
}

.uf-subject-tree .expand-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 6px;
    transition: var(--uf-transition);
    font-size: 13px;
    color: #666;
}

.uf-subject-tree .expand-icon.expanded {
    transform: rotate(90deg);
}

.uf-subject-tree .expand-icon:hover {
    color: var(--uf-primary);
}

.uf-subject-tree .children {
    margin-left: 20px;
    border-left: 1px dashed #ddd;
    padding-left: 8px;
}

.uf-expand-icon {
    transition: transform 0.2s ease;
    font-size: 13px;
}

.uf-expand-icon.expanded {
    transform: rotate(90deg);
}

/* 用友表单错误样式 */
.uf-form-control.uf-error {
    border-color: var(--uf-danger);
    box-shadow: 0 0 3px rgba(220, 53, 69, 0.3);
}

.uf-form-control:focus {
    border-color: var(--uf-primary);
    box-shadow: 0 0 3px rgba(0, 102, 204, 0.3);
}

/* 用友加载动画 */
.fa-spin {
    animation: uf-spin 1s infinite linear;
}

@keyframes uf-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 用友科目树项目样式 */
.uf-subject-item {
    padding: 6px 8px;
    border-radius: 2px;
    margin-bottom: 2px;
    cursor: pointer;
    font-size: 13px;
    transition: background-color 0.2s;
}

.uf-subject-item:hover {
    background-color: #f0f8ff;
}

.uf-subject-item.selected {
    background-color: var(--uf-primary);
    color: white;
}

.uf-subject-item code {
    background: #f0f8ff;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 13px;
}

.uf-subject-item.selected code {
    background: rgba(255,255,255,0.2);
    color: white;
}

/* 用友科目类型标题 */
.uf-subject-type-title {
    font-weight: 600;
    color: var(--uf-primary);
    border-bottom: 1px solid var(--uf-border);
    padding-bottom: 4px;
    margin-bottom: 8px;
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .uf-card-body > div[style*="grid-template-columns"] {
        grid-template-columns: 1fr !important;
        gap: 8px !important;
    }

    .uf-form-label {
        text-align: left !important;
        padding-top: 0 !important;
        margin-bottom: 4px;
        font-weight: 600;
    }

    /* 上级科目选择器响应式 */
    .uf-card-body div[style*="display: flex"] {
        flex-direction: column !important;
        gap: 8px !important;
    }

    .uf-card-body div[style*="flex: 2"],
    .uf-card-body div[style*="flex: 1"] {
        flex: none !important;
    }
}

/* 表单标签优化 */
.uf-form-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--uf-text);
    line-height: 1.4;
}
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
// 用友风格会计科目选择器 - 基于凭证创建页面的科目选择器
document.addEventListener('DOMContentLoaded', function() {
    console.log('用友风格科目选择器初始化...');

    // 基础元素
    const subjectTypeSelect = document.getElementById('subject_type');
    const balanceDirectionSelect = document.getElementById('balance_direction');
    const balanceDirectionHelp = document.getElementById('balance_direction_help');
    const codeInput = document.getElementById('code');
    const nameInput = document.getElementById('name');
    const parentIdInput = document.getElementById('parent_id');
    const parentSubjectDisplay = document.getElementById('parent-subject-display');
    const clearParentBtn = document.getElementById('clear-parent-btn');
    const selectedParent = document.getElementById('selected-parent');
    const selectedParentText = document.getElementById('selected-parent-text');
    const createModeTip = document.getElementById('create-mode-tip');

    // 模态框元素
    const parentSubjectModal = document.getElementById('parentSubjectModal');
    const modalSubjectTree = document.getElementById('modal-subject-tree');
    const subjectSearch = document.getElementById('subject-search');

    // 全局变量
    let allSubjects = [];
    let filteredSubjects = [];
    let selectedParentId = null;
    let tempSelectedParentId = null; // 模态框中临时选择的科目ID

    // 科目类型与余额方向的对应关系
    const balanceDirectionMap = {
        '资产': '借方',
        '费用': '借方',
        '负债': '贷方',
        '净资产': '贷方',
        '所有者权益': '贷方',
        '收入': '贷方'
    };

    // 科目类型说明
    const subjectTypeDescriptions = {
        '资产': '资产类科目余额方向为借方，用于核算学校食堂拥有的各种资产',
        '负债': '负债类科目余额方向为贷方，用于核算学校食堂的各种债务',
        '净资产': '净资产类科目余额方向为贷方，用于核算学校食堂的净资产',
        '所有者权益': '所有者权益类科目余额方向为贷方，用于核算学校食堂的净资产',
        '收入': '收入类科目余额方向为贷方，用于核算学校食堂的各种收入',
        '费用': '费用类科目余额方向为借方，用于核算学校食堂的各种支出'
    };

    // 用友风格科目数据加载
    function loadSubjects() {
        console.log('加载科目数据...');

        // 显示加载状态
        modalSubjectTree.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #666;">
                <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 12px; color: var(--uf-primary);"></i>
                <p style="font-size: 13px;">正在加载科目数据...</p>
            </div>
        `;

        // 使用fetch API加载数据
        fetch('/financial/accounting-subjects/api?include_system=true')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('科目数据加载成功:', data.length, '条记录');
                allSubjects = data || [];
                filteredSubjects = [...allSubjects];
                buildSubjectTree();
            })
            .catch(error => {
                console.error('加载科目数据失败:', error);
                modalSubjectTree.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 12px; color: var(--uf-danger);"></i>
                        <p style="font-size: 13px; color: var(--uf-danger);">加载失败: ${error.message}</p>
                        <button type="button" class="uf-btn uf-btn-primary uf-btn-sm" onclick="loadSubjects()">
                            <i class="fas fa-redo uf-icon"></i> 重试
                        </button>
                    </div>
                `;
            });
    }

    // 用友风格科目树构建
    function buildSubjectTree() {
        console.log('构建用友风格科目树...');

        if (!filteredSubjects || filteredSubjects.length === 0) {
            modalSubjectTree.innerHTML = `
                <div class="uf-empty-state">
                    <i class="fas fa-folder-open"></i>
                    <p>暂无科目数据</p>
                </div>
            `;
            return;
        }

        // 按科目类型分组
        const groupedSubjects = {};
        filteredSubjects.forEach(subject => {
            if (!groupedSubjects[subject.subject_type]) {
                groupedSubjects[subject.subject_type] = [];
            }
            groupedSubjects[subject.subject_type].push(subject);
        });

        let html = '<div class="uf-subject-tree">';
        const typeOrder = ['资产', '负债', '净资产', '收入', '费用'];

        typeOrder.forEach(type => {
            if (groupedSubjects[type] && groupedSubjects[type].length > 0) {
                html += `
                    <div class="subject-group">
                        <div class="subject-group-title" onclick="toggleSubjectGroup('${type}')">
                            <i class="fas fa-chevron-right group-toggle-icon" id="icon-${type}"></i>
                            ${type}类科目 (${groupedSubjects[type].length})
                        </div>
                        <div class="subject-group-content" id="content-${type}" style="display: none;">
                `;

                // 构建该类型的科目树
                const topLevelSubjects = groupedSubjects[type].filter(s => s.level === 1);
                topLevelSubjects.forEach(subject => {
                    html += buildSubjectItem(subject, groupedSubjects[type]);
                });

                html += `
                        </div>
                    </div>
                `;
            }
        });

        html += '</div>';
        modalSubjectTree.innerHTML = html;

        // 绑定科目选择事件
        bindSubjectSelectEvents();
    }

    // 构建单个科目项
    function buildSubjectItem(subject, allTypeSubjects, level = 0) {
        const children = allTypeSubjects.filter(s => s.parent_id === subject.id);
        const hasChildren = children.length > 0;
        const indent = level * 20;

        let html = `
            <div class="subject-item" data-id="${subject.id}" style="margin-left: ${indent}px;">
                <div class="subject-item-content" onclick="selectSubject(${subject.id})">
                    <div class="expand-icon" onclick="toggleSubjectItem(event, ${subject.id})" style="visibility: ${hasChildren ? 'visible' : 'hidden'};">
                        <i class="fas fa-chevron-right" id="expand-${subject.id}"></i>
                    </div>
                    <code class="uf-code" style="margin-right: 8px;">${subject.code}</code>
                    <span style="flex: 1;">${subject.name}</span>
                    <small style="color: #666; margin-left: 8px;">${subject.subject_type}</small>
                </div>
                <div class="children" id="children-${subject.id}" style="display: none;">
        `;

        children.forEach(child => {
            html += buildSubjectItem(child, allTypeSubjects, level + 1);
        });

        html += `
                </div>
            </div>
        `;

        return html;
    }

    // 用友风格交互事件处理

    // 绑定科目选择事件
    function bindSubjectSelectEvents() {
        // 科目项点击事件已在HTML中通过onclick绑定
        console.log('科目选择事件绑定完成');
    }

    // 切换科目组展开/收起
    window.toggleSubjectGroup = function(type) {
        const content = document.getElementById(`content-${type}`);
        const icon = document.getElementById(`icon-${type}`);

        if (content.style.display === 'none') {
            content.style.display = 'block';
            icon.className = 'fas fa-chevron-down group-toggle-icon';
        } else {
            content.style.display = 'none';
            icon.className = 'fas fa-chevron-right group-toggle-icon';
        }
    };

    // 切换科目项展开/收起
    window.toggleSubjectItem = function(event, subjectId) {
        event.stopPropagation();

        const children = document.getElementById(`children-${subjectId}`);
        const icon = document.getElementById(`expand-${subjectId}`);

        if (children.style.display === 'none') {
            children.style.display = 'block';
            icon.className = 'fas fa-chevron-down';
        } else {
            children.style.display = 'none';
            icon.className = 'fas fa-chevron-right';
        }
    };

    // 选择科目
    window.selectSubject = function(subjectId) {
        // 清除之前的选择
        document.querySelectorAll('.subject-item-content.selected').forEach(item => {
            item.classList.remove('selected');
        });

        // 设置新的选择
        const selectedItem = document.querySelector(`[data-id="${subjectId}"] .subject-item-content`);
        if (selectedItem) {
            selectedItem.classList.add('selected');
            tempSelectedParentId = subjectId;

            // 更新确定按钮状态
            const confirmBtn = document.querySelector('#parentSubjectModal .uf-btn-primary');
            if (confirmBtn) {
                const subject = allSubjects.find(s => s.id == subjectId);
                confirmBtn.innerHTML = `<i class="fas fa-check uf-icon"></i> 选择 "${subject ? subject.name : ''}"`;
                confirmBtn.disabled = false;
            }
        }
    };

    // 用友风格模态框控制

    // 打开科目选择器
    window.openParentSubjectSelector = function() {
        console.log('打开用友风格科目选择器');
        tempSelectedParentId = selectedParentId; // 保存当前选择

        // 加载科目数据
        loadSubjects();

        // 显示模态框
        $('#parentSubjectModal').modal('show');

        // 重置搜索
        if (subjectSearch) {
            subjectSearch.value = '';
        }

        // 重置确定按钮
        const confirmBtn = document.querySelector('#parentSubjectModal .uf-btn-primary');
        if (confirmBtn) {
            confirmBtn.innerHTML = '<i class="fas fa-check uf-icon"></i> 确定选择';
            confirmBtn.disabled = !tempSelectedParentId;
        }
    };

    // 确认选择上级科目
    window.confirmParentSelection = function() {
        if (!tempSelectedParentId) {
            alert('请先选择一个科目');
            return;
        }

        const subject = allSubjects.find(s => s.id == tempSelectedParentId);
        if (!subject) {
            alert('选择的科目无效');
            return;
        }

        // 设置选择结果
        selectedParentId = tempSelectedParentId;
        parentIdInput.value = selectedParentId;
        parentSubjectDisplay.value = `${subject.code} - ${subject.name}`;
        clearParentBtn.style.display = 'inline-block';

        // 更新状态显示
        selectedParentText.textContent = `${subject.code} - ${subject.name}`;
        selectedParent.style.display = 'block';
        createModeTip.style.display = 'none';

        // 自动设置科目类型和余额方向（继承上级科目）
        subjectTypeSelect.value = subject.subject_type;
        balanceDirectionSelect.value = subject.balance_direction;
        balanceDirectionHelp.textContent = `将在 "${subject.name}" 下创建子科目，自动继承科目类型和余额方向`;

        // 生成子科目编码建议
        generateChildCode(subject);

        // 关闭模态框
        $('#parentSubjectModal').modal('hide');

        console.log('已选择上级科目:', subject);
    };

    // 用友风格搜索功能
    window.searchSubjects = function() {
        const keyword = subjectSearch.value.trim().toLowerCase();

        if (!keyword) {
            filteredSubjects = [...allSubjects];
        } else {
            filteredSubjects = allSubjects.filter(subject =>
                subject.code.toLowerCase().includes(keyword) ||
                subject.name.toLowerCase().includes(keyword)
            );
        }

        buildSubjectTree();
        console.log('搜索结果:', filteredSubjects.length, '条记录');
    };

    // 清除搜索
    window.clearSearch = function() {
        subjectSearch.value = '';
        filteredSubjects = [...allSubjects];
        buildSubjectTree();
    };

    // 搜索框回车事件
    if (subjectSearch) {
        subjectSearch.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchSubjects();
            }
        });
    }

    // 生成子科目编码
    function generateChildCode(parentSubject) {
        const existingCodes = allSubjects
            .filter(s => s.parent_id == parentSubject.id)
            .map(s => s.code)
            .sort();

        for (let i = 1; i <= 999; i++) {
            const code = parentSubject.code + i.toString().padStart(3, '0');
            if (!existingCodes.includes(code)) {
                codeInput.value = code;
                codeInput.style.borderColor = 'var(--uf-success)';
                break;
            }
        }
    }

    // 清除上级科目选择
    window.clearParentSelection = function() {
        selectedParentId = null;
        tempSelectedParentId = null;
        parentIdInput.value = '';
        parentSubjectDisplay.value = '';
        clearParentBtn.style.display = 'none';
        selectedParent.style.display = 'none';
        createModeTip.style.display = 'block';

        // 清除模态框中的选中状态
        document.querySelectorAll('.subject-item-content.selected').forEach(item => {
            item.classList.remove('selected');
        });

        // 重置为独立科目模式
        balanceDirectionHelp.textContent = '请选择科目类型，系统将自动设置余额方向';
        generateIndependentCode();

        console.log('已清除上级科目选择');
    };

    // 生成独立科目编码
    function generateIndependentCode() {
        const selectedType = subjectTypeSelect.value;
        if (!selectedType) return;

        const typePrefix = {
            '资产': '1',
            '负债': '2',
            '净资产': '3',
            '所有者权益': '3',
            '收入': '4',
            '费用': '5'
        };

        const prefix = typePrefix[selectedType];
        if (prefix) {
            const existingCodes = allSubjects
                .filter(s => s.subject_type === selectedType && s.level === 1)
                .map(s => s.code)
                .sort();

            for (let i = 1; i <= 999; i++) {
                const code = prefix + i.toString().padStart(3, '0');
                if (!existingCodes.includes(code)) {
                    codeInput.value = code;
                    codeInput.style.borderColor = 'var(--uf-success)';
                    break;
                }
            }
        }
    }

    // 科目类型变化时自动设置余额方向
    function updateBalanceDirection() {
        const selectedType = subjectTypeSelect.value;

        if (selectedParentId) {
            // 如果已选择上级科目，不允许修改科目类型
            const parentSubject = allSubjects.find(s => s.id == selectedParentId);
            if (parentSubject && selectedType !== parentSubject.subject_type) {
                alert('子科目必须与上级科目保持相同的科目类型');
                subjectTypeSelect.value = parentSubject.subject_type;
                return;
            }
        } else {
            // 独立科目模式：根据科目类型设置余额方向
            if (selectedType && balanceDirectionMap[selectedType]) {
                balanceDirectionSelect.value = balanceDirectionMap[selectedType];
                balanceDirectionHelp.textContent = `${subjectTypeDescriptions[selectedType]}`;
                generateIndependentCode();
            } else {
                balanceDirectionHelp.textContent = '请选择科目类型，系统将自动设置余额方向';
            }
        }
    }

    // 监听科目类型变化
    if (subjectTypeSelect) {
        subjectTypeSelect.addEventListener('change', updateBalanceDirection);
    }

    // 实时编码验证
    if (codeInput) {
        codeInput.addEventListener('input', function() {
            const code = this.value.trim();
            if (code && allSubjects.length > 0) {
                const existingSubject = allSubjects.find(s => s.code === code);
                if (existingSubject) {
                    this.style.borderColor = 'var(--uf-danger)';
                    this.title = `编码已被使用：${existingSubject.name}`;
                } else {
                    this.style.borderColor = 'var(--uf-success)';
                    this.title = '编码可用';
                }
            } else {
                this.style.borderColor = '';
                this.title = '';
            }
        });
    }

    // 表单验证
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const code = codeInput.value.trim();
            const name = nameInput.value.trim();

            if (!code) {
                alert('请输入科目编码');
                e.preventDefault();
                return false;
            }

            if (!name) {
                alert('请输入科目名称');
                e.preventDefault();
                return false;
            }

            // 检查科目编码是否重复
            if (allSubjects.length > 0) {
                const existingSubject = allSubjects.find(s => s.code === code);
                if (existingSubject) {
                    alert(`科目编码 ${code} 已被使用：${existingSubject.name}`);
                    e.preventDefault();
                    return false;
                }
            }

            console.log('表单验证通过，提交数据');
        });
    }

    // 初始化
    console.log('用友风格科目选择器初始化完成');
    updateBalanceDirection();

    // 将loadSubjects设为全局函数，以便重试按钮调用
    window.loadSubjects = loadSubjects;
});

// 用友风格科目选择器全局函数
// 这些函数需要在全局作用域中，以便HTML中的onclick事件调用
</script>
{% endblock %}
