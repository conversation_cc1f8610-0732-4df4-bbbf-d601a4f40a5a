{% extends "financial/base.html" %}

{% block title %}总账查询{% endblock %}

{% block page_title %}总账查询{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务报表</a></span>
<span class="uf-breadcrumb-item active">总账查询</span>
{% endblock %}

{% block page_actions %}
{% endblock %}

{% block financial_content %}
<!-- 用友风格总账查询窗口 -->
<div class="uf-general-ledger-container">
    <!-- 查询条件和操作按钮一行显示 -->
    <div class="uf-card">
        <div class="uf-card-body" style="padding: 12px 16px;">
            <form method="GET" class="uf-query-form">
                <div class="uf-query-actions-row">
                    <!-- 查询条件组 -->
                    <div class="uf-query-conditions">
                        <div class="uf-form-group">
                            <label class="uf-form-label">开始日期：</label>
                            <input type="date" class="uf-form-control" id="start_date" name="start_date"
                                   value="{{ start_date }}" required>
                        </div>
                        <div class="uf-form-group">
                            <label class="uf-form-label">结束日期：</label>
                            <input type="date" class="uf-form-control" id="end_date" name="end_date"
                                   value="{{ end_date }}" required>
                        </div>
                        <div class="uf-form-group">
                            <label class="uf-form-label">科目类型：</label>
                            <select class="uf-form-control" id="subject_type" name="subject_type">
                                <option value="">全部类型</option>
                                <option value="资产" {% if subject_type == '资产' %}selected{% endif %}>资产</option>
                                <option value="负债" {% if subject_type == '负债' %}selected{% endif %}>负债</option>
                                <option value="所有者权益" {% if subject_type == '所有者权益' %}selected{% endif %}>所有者权益</option>
                                <option value="收入" {% if subject_type == '收入' %}selected{% endif %}>收入</option>
                                <option value="费用" {% if subject_type == '费用' %}selected{% endif %}>费用</option>
                            </select>
                        </div>
                    </div>

                    <!-- 操作按钮组 -->
                    <div class="uf-query-actions">
                        <button type="submit" class="uf-btn uf-btn-primary">
                            <i class="fas fa-search uf-icon"></i> 查询
                        </button>
                        <button type="button" class="uf-btn uf-btn-secondary" onclick="resetQueryForm()">
                            <i class="fas fa-undo uf-icon"></i> 重置
                        </button>
                        <button type="button" class="uf-btn uf-btn-success" onclick="exportGeneralLedger()">
                            <i class="fas fa-file-excel uf-icon"></i> 导出
                        </button>
                        <button type="button" class="uf-btn uf-btn-info" onclick="printGeneralLedger()">
                            <i class="fas fa-print uf-icon"></i> 打印
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 总账汇总表 -->
    <div class="uf-card">
        <div class="uf-card-header">
            <div class="uf-card-header-title">
                <i class="fas fa-table uf-card-header-icon"></i>
                总账汇总表
            </div>
            <div class="uf-card-header-actions">
                <span class="uf-record-count">
                    {% if general_ledger_data %}
                        共 {{ general_ledger_data|length }} 个科目
                    {% else %}
                        无数据
                    {% endif %}
                </span>
            </div>
        </div>
        <div class="uf-card-body" style="padding: 0;">
            <div class="uf-table-container">
                <table class="uf-table uf-general-ledger-table">
                    <thead>
                        <tr>
                            <th style="width: 100px;">科目编码</th>
                            <th style="width: 200px;">科目名称</th>
                            <th style="width: 80px;">科目类型</th>
                            <th style="width: 80px;">余额方向</th>
                            <th class="uf-amount-col" style="width: 120px;">期初余额</th>
                            <th class="uf-amount-col" style="width: 120px;">本期借方</th>
                            <th class="uf-amount-col" style="width: 120px;">本期贷方</th>
                            <th class="uf-amount-col" style="width: 120px;">期末余额</th>
                            <th style="width: 80px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% set total_opening = 0 %}
                        {% set total_debit = 0 %}
                        {% set total_credit = 0 %}
                        {% set total_ending = 0 %}

                        {% for item in general_ledger_data %}
                        {% set total_opening = total_opening + item.opening_balance %}
                        {% set total_debit = total_debit + item.period_debit %}
                        {% set total_credit = total_credit + item.period_credit %}
                        {% set total_ending = total_ending + item.ending_balance %}

                        <tr>
                            <td class="uf-subject-code">{{ item.code }}</td>
                            <td class="uf-subject-name">{{ item.name }}</td>
                            <td class="text-center">
                                <span class="uf-status uf-status-info">{{ item.subject_type }}</span>
                            </td>
                            <td class="text-center">
                                <span class="uf-status {% if item.balance_direction == '借方' %}uf-status-primary{% else %}uf-status-warning{% endif %}">
                                    {{ item.balance_direction }}
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                {% if item.opening_balance != 0 %}
                                    <span class="uf-currency">¥</span><span class="uf-amount">{{ "%.2f"|format(item.opening_balance|float) }}</span>
                                {% else %}
                                    <span class="uf-amount-zero">-</span>
                                {% endif %}
                            </td>
                            <td class="uf-amount-col">
                                {% if item.period_debit > 0 %}
                                    <span class="uf-currency">¥</span><span class="uf-amount">{{ "%.2f"|format(item.period_debit|float) }}</span>
                                {% else %}
                                    <span class="uf-amount-zero">-</span>
                                {% endif %}
                            </td>
                            <td class="uf-amount-col">
                                {% if item.period_credit > 0 %}
                                    <span class="uf-currency">¥</span><span class="uf-amount">{{ "%.2f"|format(item.period_credit|float) }}</span>
                                {% else %}
                                    <span class="uf-amount-zero">-</span>
                                {% endif %}
                            </td>
                            <td class="uf-amount-col">
                                {% if item.ending_balance != 0 %}
                                    <span class="uf-currency">¥</span><span class="uf-amount uf-amount-bold">{{ "%.2f"|format(item.ending_balance|float) }}</span>
                                {% else %}
                                    <span class="uf-amount-zero">-</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <a href="{{ url_for('financial.detail_ledger', subject_id=item.id, start_date=start_date, end_date=end_date) }}"
                                   class="uf-btn uf-btn-sm uf-btn-info" title="查看明细账">
                                    <i class="fas fa-list uf-icon"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}

                        {% if not general_ledger_data %}
                        <tr class="empty-row">
                            <td colspan="9">没有找到相关数据</td>
                        </tr>
                        {% endif %}
                    </tbody>

                    {% if general_ledger_data %}
                    <tfoot>
                        <tr class="uf-total-row">
                            <th colspan="4">合计</th>
                            <th class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-total">{{ "%.2f"|format(total_opening|float) }}</span>
                            </th>
                            <th class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-total">{{ "%.2f"|format(total_debit|float) }}</span>
                            </th>
                            <th class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-total">{{ "%.2f"|format(total_credit|float) }}</span>
                            </th>
                            <th class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-total">{{ "%.2f"|format(total_ending|float) }}</span>
                            </th>
                            <th></th>
                        </tr>
                    </tfoot>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>

    <!-- 试算平衡检查 -->
    {% if general_ledger_data %}
    <div class="uf-card">
        <div class="uf-card-header">
            <div class="uf-card-header-title">
                <i class="fas fa-balance-scale uf-card-header-icon"></i>
                试算平衡检查
            </div>
            <div class="uf-card-header-actions">
                {% set balance_diff = total_debit - total_credit %}
                {% if balance_diff == 0 %}
                    <span class="uf-status uf-status-success">✓ 试算平衡</span>
                {% else %}
                    <span class="uf-status uf-status-danger">✗ 试算不平衡</span>
                {% endif %}
            </div>
        </div>
        <div class="uf-card-body">
            <div class="uf-balance-check-grid">
                <div class="uf-balance-item">
                    <div class="uf-balance-label">本期借方发生额合计：</div>
                    <div class="uf-balance-value">
                        <span class="uf-currency">¥</span><span class="uf-amount uf-amount-large">{{ "%.2f"|format(total_debit|float) }}</span>
                    </div>
                </div>
                <div class="uf-balance-item">
                    <div class="uf-balance-label">本期贷方发生额合计：</div>
                    <div class="uf-balance-value">
                        <span class="uf-currency">¥</span><span class="uf-amount uf-amount-large">{{ "%.2f"|format(total_credit|float) }}</span>
                    </div>
                </div>
                <div class="uf-balance-item">
                    <div class="uf-balance-label">借贷差额：</div>
                    <div class="uf-balance-value">
                        {% set balance_diff = total_debit - total_credit %}
                        <span class="uf-currency">¥</span>
                        <span class="uf-amount uf-amount-large {% if balance_diff == 0 %}uf-amount-success{% else %}uf-amount-danger{% endif %}">
                            {{ "%.2f"|format(balance_diff|float) }}
                        </span>
                    </div>
                </div>
                <div class="uf-balance-item">
                    <div class="uf-balance-label">平衡状态：</div>
                    <div class="uf-balance-value">
                        {% if balance_diff == 0 %}
                            <span class="uf-status uf-status-success">✓ 试算平衡</span>
                        {% else %}
                            <span class="uf-status uf-status-danger">✗ 试算不平衡</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 按科目类型汇总 -->
    {% if general_ledger_data %}
    <div class="uf-card">
        <div class="uf-card-header">
            <div class="uf-card-header-title">
                <i class="fas fa-chart-pie uf-card-header-icon"></i>
                按科目类型汇总
            </div>
        </div>
        <div class="uf-card-body" style="padding: 0;">
            <div class="uf-table-container">
                <table class="uf-table uf-summary-table">
                    <thead>
                        <tr>
                            <th style="width: 200px;">科目类型</th>
                            <th class="uf-amount-col" style="width: 150px;">期末余额</th>
                            <th class="text-center" style="width: 100px;">科目数量</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% set type_summary = {} %}
                        {% for item in general_ledger_data %}
                            {% if item.subject_type not in type_summary %}
                                {% set _ = type_summary.update({item.subject_type: {'balance': 0, 'count': 0}}) %}
                            {% endif %}
                            {% set _ = type_summary[item.subject_type].update({
                                'balance': type_summary[item.subject_type]['balance'] + item.ending_balance,
                                'count': type_summary[item.subject_type]['count'] + 1
                            }) %}
                        {% endfor %}

                        {% for type_name, summary in type_summary.items() %}
                        <tr>
                            <td>
                                <span class="uf-status uf-status-info">{{ type_name }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount">{{ "%.2f"|format(summary.balance|float) }}</span>
                            </td>
                            <td class="text-center">
                                <span class="uf-count-badge">{{ summary.count }}</span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block financial_js %}
<script>
// 用友风格总账查询页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initUFGeneralLedger();
});

function initUFGeneralLedger() {
    // 初始化表格排序
    initTableSorting();

    // 初始化快捷键
    initKeyboardShortcuts();

    // 初始化工具提示
    initTooltips();
}

function initTableSorting() {
    // 为表头添加排序功能
    const headers = document.querySelectorAll('.uf-general-ledger-table th');
    headers.forEach((header, index) => {
        if (index < 8) { // 前8列可排序
            header.classList.add('sortable');
            header.addEventListener('click', () => sortTable(index));
        }
    });
}

function sortTable(columnIndex) {
    const table = document.querySelector('.uf-general-ledger-table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr:not(.empty-row)'));

    if (rows.length === 0) return;

    // 获取当前排序状态
    const header = table.querySelectorAll('th')[columnIndex];
    const isAsc = !header.classList.contains('sort-asc');

    // 清除所有排序标记
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });

    // 设置当前排序标记
    header.classList.add(isAsc ? 'sort-asc' : 'sort-desc');

    // 排序行
    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();

        // 数字列排序
        if (columnIndex >= 4 && columnIndex <= 7) {
            const aNum = parseFloat(aValue.replace(/[¥,\-]/g, '')) || 0;
            const bNum = parseFloat(bValue.replace(/[¥,\-]/g, '')) || 0;
            return isAsc ? aNum - bNum : bNum - aNum;
        }

        // 文本列排序
        return isAsc ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    });

    // 重新插入排序后的行
    rows.forEach(row => tbody.appendChild(row));
}

function initKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+E: 导出
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            exportGeneralLedger();
        }

        // Ctrl+P: 打印
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printGeneralLedger();
        }

        // F5: 刷新查询
        if (e.key === 'F5') {
            e.preventDefault();
            document.querySelector('.uf-query-form').submit();
        }
    });
}

function initTooltips() {
    // 为金额单元格添加工具提示
    const amountCells = document.querySelectorAll('.uf-amount');
    amountCells.forEach(cell => {
        const value = cell.textContent.replace(/[¥,]/g, '');
        if (value && value !== '-') {
            cell.title = `金额：${value}`;
        }
    });
}

function exportGeneralLedger() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const subjectType = document.getElementById('subject_type').value;

    if (!startDate || !endDate) {
        alert('请选择查询日期范围');
        return;
    }

    const url = `{{ url_for('financial.export_report', report_type='general_ledger') }}?start_date=${startDate}&end_date=${endDate}&subject_type=${subjectType}`;
    window.open(url, '_blank');
}

function printGeneralLedger() {
    // 创建打印窗口
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
}

function generatePrintContent() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const subjectType = document.getElementById('subject_type').value;

    const table = document.querySelector('.uf-general-ledger-table').outerHTML;

    return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>总账查询报表</title>
            <style>
                body { font-family: 'Microsoft YaHei', sans-serif; font-size: 12px; }
                .print-header { text-align: center; margin-bottom: 20px; }
                .print-info { margin-bottom: 10px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #999; padding: 4px 6px; text-align: center; }
                th { background: #f0f8ff; font-weight: 600; }
                .uf-amount-col { text-align: right; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <div class="print-header">
                <h2>总账查询报表</h2>
                <div class="print-info">查询期间：${startDate} 至 ${endDate}</div>
                ${subjectType ? `<div class="print-info">科目类型：${subjectType}</div>` : ''}
                <div class="print-info">打印时间：${new Date().toLocaleString()}</div>
            </div>
            ${table}
        </body>
        </html>
    `;
}

function resetQueryForm() {
    const today = new Date().toISOString().split('T')[0];
    const firstDay = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];

    document.getElementById('start_date').value = firstDay;
    document.getElementById('end_date').value = today;
    document.getElementById('subject_type').value = '';
}
</script>
{% endblock %}

{% block financial_css %}
<style>
/* 用友风格总账查询页面专用样式 */
.uf-general-ledger-container {
    background: var(--uf-light);
    min-height: 100vh;
    padding: 0;
}

/* 查询表单样式 */
.uf-query-form {
    margin: 0;
}

/* 查询条件和操作按钮一行布局 */
.uf-query-actions-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
}

.uf-query-conditions {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
    min-width: 0;
}

.uf-query-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 0 0 auto;
}

.uf-form-group {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
    min-width: 0;
}

.uf-form-label {
    white-space: nowrap;
    font-size: 11px;
    color: #333;
    font-weight: 500;
}

.uf-form-control {
    flex: 1;
    min-width: 120px;
    height: 28px;
    font-size: 11px;
}

/* 总账表格专用样式 */
.uf-general-ledger-table {
    font-size: 11px;
}

.uf-general-ledger-table th {
    background: linear-gradient(to bottom, #e6f2ff 0%, #cce5ff 100%);
    color: var(--uf-primary);
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
    position: relative;
}

.uf-general-ledger-table th.sortable {
    cursor: pointer;
    user-select: none;
}

.uf-general-ledger-table th.sortable:hover {
    background: linear-gradient(to bottom, #d9e8ff 0%, #b3d9ff 100%);
}

.uf-general-ledger-table th.sort-asc::after {
    content: "↑";
    position: absolute;
    right: 4px;
    font-size: 13px;
    color: var(--uf-primary);
}

.uf-general-ledger-table th.sort-desc::after {
    content: "↓";
    position: absolute;
    right: 4px;
    font-size: 13px;
    color: var(--uf-primary);
}

.uf-general-ledger-table td {
    padding: 3px 6px;
    vertical-align: middle;
    border: 1px solid var(--uf-grid-border);
}

.uf-general-ledger-table tbody tr:hover {
    background: var(--uf-row-hover);
}

/* 科目编码和名称样式 */
.uf-subject-code {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--uf-primary);
    font-size: 11px;
}

.uf-subject-name {
    font-weight: 500;
    color: #333;
}

/* 金额显示样式 */
.uf-amount-zero {
    color: var(--uf-muted);
    font-style: italic;
}

.uf-amount-bold {
    font-weight: 600;
    color: #333;
}

.uf-amount-large {
    font-size: 13px;
    font-weight: 600;
}

.uf-amount-success {
    color: var(--uf-success);
}

.uf-amount-danger {
    color: var(--uf-danger);
}

.uf-amount-total {
    font-weight: 700;
    color: var(--uf-primary);
}

/* 合计行样式 */
.uf-total-row {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    font-weight: 600;
}

.uf-total-row th {
    background: linear-gradient(to bottom, #e6f2ff 0%, #d9e8ff 100%);
    color: var(--uf-primary);
    font-weight: 700;
}

/* 试算平衡检查样式 */
.uf-balance-check-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    padding: 8px;
}

.uf-balance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
}

.uf-balance-label {
    font-weight: 500;
    color: #333;
    font-size: 11px;
}

.uf-balance-value {
    font-weight: 600;
    font-size: 12px;
}

/* 汇总表样式 */
.uf-summary-table {
    font-size: 11px;
}

.uf-summary-table th {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
}

.uf-count-badge {
    display: inline-block;
    background: var(--uf-primary);
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 13px;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

/* 记录数量显示 */
.uf-record-count {
    font-size: 11px;
    color: var(--uf-muted);
    font-weight: normal;
}

/* 状态标签样式扩展 */
.uf-status-primary {
    background: var(--uf-primary);
    color: white;
    border-color: var(--uf-primary);
}

.uf-status-info {
    background: var(--uf-info);
    color: white;
    border-color: var(--uf-info);
}

.uf-status-warning {
    background: var(--uf-warning);
    color: #333;
    border-color: var(--uf-warning);
}

.uf-status-success {
    background: var(--uf-success);
    color: white;
    border-color: var(--uf-success);
}

.uf-status-danger {
    background: var(--uf-danger);
    color: white;
    border-color: var(--uf-danger);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .uf-query-actions-row {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .uf-query-conditions {
        justify-content: center;
    }

    .uf-query-actions {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .uf-query-conditions {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .uf-form-group {
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .uf-form-control {
        min-width: auto;
        width: 100%;
    }

    .uf-query-actions {
        flex-wrap: wrap;
        gap: 6px;
    }

    .uf-balance-check-grid {
        grid-template-columns: 1fr;
    }

    .uf-general-ledger-table {
        font-size: 13px;
    }

    .uf-general-ledger-table th,
    .uf-general-ledger-table td {
        padding: 2px 4px;
    }
}

/* 打印样式 */
@media print {
    .uf-card-header,
    .uf-btn,
    .page-actions {
        display: none !important;
    }

    .uf-card {
        border: none;
        box-shadow: none;
        margin: 0;
    }

    .uf-card-body {
        padding: 0;
    }

    .uf-general-ledger-table {
        font-size: 13px;
    }

    .uf-general-ledger-table th,
    .uf-general-ledger-table td {
        padding: 2px 4px;
    }
}
</style>
{% endblock %}
