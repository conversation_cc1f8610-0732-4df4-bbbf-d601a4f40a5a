{% extends "financial/base.html" %}

{% block title %}成本分析报表{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-separator">/</span>
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务报表</a></span>
<span class="uf-breadcrumb-separator">/</span>
<span class="uf-breadcrumb-item active">成本分析报表</span>
{% endblock %}

{% block page_title %}成本分析报表{% endblock %}

{% block page_actions %}
<button type="button" class="uf-btn uf-btn-primary" onclick="exportReport()">
    <i class="fas fa-download uf-icon"></i> 导出报表
</button>
{% endblock %}

{% block financial_content %}
<!-- 用友风格查询条件区域 -->
<div style="background: #f8f9fa; border: 1px solid #c0c0c0; padding: 8px; margin-bottom: 8px; border-radius: 1px;">
    <form method="GET" style="margin: 0;">
        <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
            <div style="display: flex; align-items: center; gap: 4px;">
                <label style="font-size: 11px; color: #333; white-space: nowrap;">开始日期：</label>
                <input type="date" name="start_date" value="{{ start_date }}"
                       style="width: 120px; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
            </div>
            <div style="display: flex; align-items: center; gap: 4px;">
                <label style="font-size: 11px; color: #333; white-space: nowrap;">结束日期：</label>
                <input type="date" name="end_date" value="{{ end_date }}"
                       style="width: 120px; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
            </div>
            <div style="display: flex; align-items: center; gap: 4px;">
                <label style="font-size: 11px; color: #333; white-space: nowrap;">分析类型：</label>
                <select name="analysis_type" style="width: 100px; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
                    <option value="monthly" {% if analysis_type == 'monthly' %}selected{% endif %}>按月分析</option>
                    <option value="daily" {% if analysis_type == 'daily' %}selected{% endif %}>按日分析</option>
                    <option value="ingredient" {% if analysis_type == 'ingredient' %}selected{% endif %}>按食材分析</option>
                </select>
            </div>
            <div style="display: flex; gap: 4px;">
                <button type="submit" class="uf-btn uf-btn-primary uf-btn-sm">
                    <i class="fas fa-search" style="font-size: 10px;"></i> 查询
                </button>
                <button type="button" class="uf-btn uf-btn-sm" onclick="resetForm()">
                    <i class="fas fa-undo" style="font-size: 10px;"></i> 重置
                </button>
            </div>
        </div>
    </form>
</div>

<!-- 用友风格成本分析汇总 -->
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-chart-line uf-icon"></i> 成本分析汇总
        <span style="margin-left: 6px; padding: 1px 4px; background: var(--uf-info); color: white; border-radius: 1px; font-size: 13px;">
            分析期间：{{ start_date }} 至 {{ end_date }}
        </span>
    </div>
    <div class="uf-card-body" style="padding: 0;">
        <table class="uf-table" style="margin: 0;">
            <thead>
                <tr>
                    <th style="width: 25%;">总成本</th>
                    <th style="width: 25%;">记录数量</th>
                    <th style="width: 25%;">平均成本</th>
                    <th style="width: 25%;">分析类型</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="uf-text-right">
                        <span class="uf-currency">¥</span>{{ "%.2f"|format(cost_analysis_data.total_cost) }}
                    </td>
                    <td class="uf-text-center">
                        {{ cost_analysis_data.cost_data|length }}
                    </td>
                    <td class="uf-text-right">
                        <span class="uf-currency">¥</span>
                        {% if cost_analysis_data.cost_data|length > 0 %}
                            {{ "%.2f"|format(cost_analysis_data.total_cost / cost_analysis_data.cost_data|length) }}
                        {% else %}
                            0.00
                        {% endif %}
                    </td>
                    <td class="uf-text-center">
                        <span style="background: #e6f2ff; color: var(--uf-primary); border: 1px solid #b3d9ff; padding: 1px 3px; border-radius: 1px; font-size: 13px; white-space: nowrap;">
                            {% if analysis_type == 'monthly' %}按月分析
                            {% elif analysis_type == 'daily' %}按日分析
                            {% else %}按食材分析{% endif %}
                        </span>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- 用友风格成本分析明细 -->
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-list uf-icon"></i> 成本分析明细
        <span style="margin-left: 6px; padding: 1px 4px; background: var(--uf-info); color: white; border-radius: 1px; font-size: 13px;">
            共 {{ cost_analysis_data.cost_data|length }} 条记录
        </span>
    </div>
    <div class="uf-card-body" style="padding: 0;">
        {% if cost_analysis_data.cost_data %}
        <table class="uf-table" style="margin: 0;">
            <thead>
                <tr>
                    {% if analysis_type == 'monthly' or analysis_type == 'daily' %}
                    <th style="width: 150px;">期间</th>
                    <th style="width: 120px;">总成本</th>
                    <th style="width: 100px;">入库次数</th>
                    <th style="width: 120px;">平均成本</th>
                    {% else %}
                    <th style="width: 200px;">食材名称</th>
                    <th style="width: 120px;">食材类别</th>
                    <th style="width: 100px;">总数量</th>
                    <th style="width: 120px;">总成本</th>
                    <th style="width: 100px;">平均单价</th>
                    <th style="width: 100px;">入库次数</th>
                    {% endif %}
                    <th style="width: 80px;">占比</th>
                </tr>
            </thead>
            <tbody>
                {% for item in cost_analysis_data.cost_data %}
                <tr>
                    {% if analysis_type == 'monthly' or analysis_type == 'daily' %}
                    <td class="uf-text-left">{{ item.period }}</td>
                    <td class="uf-text-right"><span class="uf-currency">¥</span>{{ "%.2f"|format(item.total_cost) }}</td>
                    <td class="uf-text-center">{{ item.stock_in_count }}</td>
                    <td class="uf-text-right"><span class="uf-currency">¥</span>{{ "%.2f"|format(item.avg_cost) }}</td>
                    {% else %}
                    <td class="uf-text-left">{{ item.ingredient_name }}</td>
                    <td class="uf-text-left">{{ item.category or '未分类' }}</td>
                    <td class="uf-text-right">{{ "%.2f"|format(item.total_quantity) }}</td>
                    <td class="uf-text-right"><span class="uf-currency">¥</span>{{ "%.2f"|format(item.total_cost) }}</td>
                    <td class="uf-text-right"><span class="uf-currency">¥</span>{{ "%.2f"|format(item.avg_unit_cost) }}</td>
                    <td class="uf-text-center">{{ item.stock_in_count }}</td>
                    {% endif %}
                    <td class="uf-text-right">
                        {% if cost_analysis_data.total_cost > 0 %}
                            {{ "%.1f"|format(item.total_cost / cost_analysis_data.total_cost * 100) }}%
                        {% else %}
                            0.0%
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div style="text-align: center; padding: 40px; color: #666;">
            <i class="fas fa-inbox" style="font-size: 48px; color: #ccc; margin-bottom: 16px;"></i>
            <p style="margin-bottom: 20px;">暂无成本分析数据</p>
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 2px; padding: 16px; max-width: 500px; margin: 0 auto; text-align: left;">
                <h6 style="color: #856404; margin-bottom: 12px;">
                    <i class="fas fa-exclamation-triangle"></i> 提示
                </h6>
                <p style="margin-bottom: 12px; font-size: 12px; color: #856404;">
                    请调整查询条件或检查是否有相关的入库记录。
                </p>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 用友风格成本趋势图表 -->
{% if cost_analysis_data.cost_data|length > 0 and analysis_type != 'ingredient' %}
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-chart-area uf-icon"></i> 成本趋势图
    </div>
    <div class="uf-card-body">
        <canvas id="costChart" width="400" height="200"></canvas>
    </div>
</div>
{% endif %}

<script>
function resetForm() {
    const today = new Date().toISOString().split('T')[0];
    const firstDay = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
    
    document.querySelector('input[name="start_date"]').value = firstDay;
    document.querySelector('input[name="end_date"]').value = today;
    document.querySelector('select[name="analysis_type"]').value = 'monthly';
}

function exportReport() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.open(`${window.location.pathname}?${params.toString()}`);
}
</script>
{% endblock %}
