{% extends "financial/base.html" %}

{% block page_title %}应付账款账龄分析{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-separator">/</span>
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务报表</a></span>
<span class="uf-breadcrumb-separator">/</span>
<span class="uf-breadcrumb-item active">应付账款账龄分析</span>
{% endblock %}

{% block page_actions %}
<a href="{{ url_for('financial.reports_index') }}" class="uf-btn">
    <i class="fas fa-arrow-left uf-icon"></i> 返回报表列表
</a>
<button type="button" class="uf-btn uf-btn-primary" onclick="exportReport()">
    <i class="fas fa-download uf-icon"></i> 导出报表
</button>
{% endblock %}

{% block financial_content %}
<!-- 用友风格查询条件区域 -->
<div style="background: #f8f9fa; border: 1px solid #c0c0c0; padding: 8px; margin-bottom: 8px; border-radius: 1px;">
    <form method="GET" style="margin: 0;">
        <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
            <div style="display: flex; align-items: center; gap: 4px;">
                <label style="font-size: 11px; color: #333; white-space: nowrap;">分析日期：</label>
                <input type="date" id="aging_date" name="aging_date" value="{{ aging_date }}"
                       onchange="this.form.submit()"
                       style="width: 120px; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
            </div>
            <div style="display: flex; align-items: center; gap: 4px;">
                <label style="font-size: 11px; color: #333; white-space: nowrap;">供应商：</label>
                <select id="supplier_id" name="supplier_id" onchange="this.form.submit()"
                        style="width: 150px; font-size: 11px; padding: 2px 4px; border: 1px solid #c0c0c0; border-radius: 1px;">
                    <option value="">全部供应商</option>
                    {% for supplier in suppliers %}
                    <option value="{{ supplier.id }}" {% if supplier_id == supplier.id %}selected{% endif %}>
                        {{ supplier.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div style="display: flex; gap: 4px;">
                <button type="submit" class="uf-btn uf-btn-primary uf-btn-sm">
                    <i class="fas fa-sync" style="font-size: 10px;"></i> 刷新报表
                </button>
            </div>
        </div>
    </form>
</div>

<!-- 用友风格账龄汇总 -->
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-chart-pie uf-icon"></i> 账龄汇总
        <span style="margin-left: 6px; padding: 1px 4px; background: var(--uf-info); color: white; border-radius: 1px; font-size: 13px;">
            分析日期：{{ aging_date }}
        </span>
    </div>
    <div class="uf-card-body" style="padding: 0;">
        <table class="uf-table" style="margin: 0;">
            <thead>
                <tr>
                    <th style="width: 25%;">账龄区间</th>
                    <th style="width: 25%;">金额</th>
                    <th style="width: 25%;">占比</th>
                    <th style="width: 25%;">笔数</th>
                </tr>
            </thead>
            <tbody>
                {% for group_name, group_data in aging_data.aging_groups.items() %}
                <tr>
                    <td class="uf-text-left">{{ group_name }}</td>
                    <td class="uf-text-right">
                        <span class="uf-currency">¥</span>{{ "%.2f"|format(group_data.amount) }}
                    </td>
                    <td class="uf-text-right">{{ "%.1f"|format(group_data.percentage) }}%</td>
                    <td class="uf-text-center">{{ group_data.count }}</td>
                </tr>
                {% endfor %}
                <tr style="background: #f0f8ff; font-weight: 600;">
                    <td class="uf-text-left">合计</td>
                    <td class="uf-text-right">
                        <span class="uf-currency">¥</span>{{ "%.2f"|format(aging_data.total_amount) }}
                    </td>
                    <td class="uf-text-right">100.0%</td>
                    <td class="uf-text-center">{{ aging_data.total_count }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- 用友风格应付账款明细 -->
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-list uf-icon"></i> 应付账款明细
        <span style="margin-left: 6px; padding: 1px 4px; background: var(--uf-info); color: white; border-radius: 1px; font-size: 13px;">
            共 {{ aging_data.total_count }} 笔
        </span>
    </div>
    <div class="uf-card-body" style="padding: 0;">
        {% if aging_data.total_count > 0 %}
        <table class="uf-table" style="margin: 0;">
            <thead>
                <tr>
                    <th style="width: 15%;">供应商</th>
                    <th style="width: 15%;">应付账款号</th>
                    <th style="width: 12%;">原始金额</th>
                    <th style="width: 12%;">已付金额</th>
                    <th style="width: 12%;">余额</th>
                    <th style="width: 8%;">账龄</th>
                    <th style="width: 10%;">账龄区间</th>
                    <th style="width: 8%;">入库日期</th>
                    <th style="width: 8%;">入库单号</th>
                </tr>
            </thead>
            <tbody>
                {% for group_name, group_data in aging_data.aging_groups.items() %}
                    {% for item in group_data['items'] %}
                    <tr>
                        <td class="uf-text-left">{{ item.supplier_name }}</td>
                        <td class="uf-text-left">{{ item.payable_number }}</td>
                        <td class="uf-text-right">
                            <span class="uf-currency">¥</span>{{ "%.2f"|format(item.original_amount) }}
                        </td>
                        <td class="uf-text-right">
                            <span class="uf-currency">¥</span>{{ "%.2f"|format(item.paid_amount) }}
                        </td>
                        <td class="uf-text-right">
                            <span class="uf-currency">¥</span>{{ "%.2f"|format(item.balance_amount) }}
                        </td>
                        <td class="uf-text-center">{{ item.aging_days }}天</td>
                        <td class="uf-text-center">
                            <span style="background:
                                {% if item.aging_days <= 30 %}#d4edda; color: #155724; border: 1px solid #c3e6cb;
                                {% elif item.aging_days <= 60 %}#d1ecf1; color: #0c5460; border: 1px solid #bee5eb;
                                {% elif item.aging_days <= 90 %}#fff3cd; color: #856404; border: 1px solid #ffeaa7;
                                {% elif item.aging_days <= 180 %}#f8d7da; color: #721c24; border: 1px solid #f5c6cb;
                                {% else %}#d6d8db; color: #383d41; border: 1px solid #c6c8ca;{% endif %}
                                padding: 1px 3px; border-radius: 1px; font-size: 13px; white-space: nowrap;">
                                {{ group_name }}
                            </span>
                        </td>
                        <td class="uf-text-center">{{ item.stock_in_date.strftime('%m-%d') if item.stock_in_date else '-' }}</td>
                        <td class="uf-text-left">{{ item.stock_in_number or '-' }}</td>
                    </tr>
                    {% endfor %}
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div style="text-align: center; padding: 40px; color: #666;">
            <i class="fas fa-inbox" style="font-size: 48px; color: #ccc; margin-bottom: 16px;"></i>
            <p style="margin-bottom: 20px;">暂无应付账款数据</p>
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 2px; padding: 16px; max-width: 500px; margin: 0 auto; text-align: left;">
                <h6 style="color: #856404; margin-bottom: 12px;">
                    <i class="fas fa-exclamation-triangle"></i> 提示
                </h6>
                <p style="margin-bottom: 12px; font-size: 12px; color: #856404;">
                    请检查是否有未付清的应付账款记录。
                </p>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 用友风格报表说明 -->
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-info-circle uf-icon"></i> 报表说明
    </div>
    <div class="uf-card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 8px; font-size: 11px;">
            <div><strong>分析日期：</strong>{{ aging_date }}</div>
            <div><strong>编制单位：</strong>{{ user_area.name }}</div>
            <div><strong>金额单位：</strong>人民币元</div>
            <div><strong>账龄计算：</strong>从入库日期到分析日期</div>
            <div><strong>统计范围：</strong>未付清的应付账款</div>
            <div><strong>生成时间：</strong>{{ current_time.strftime('%Y-%m-%d %H:%M') if current_time else '' }}</div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    const agingDate = document.getElementById('aging_date').value;
    const supplierId = document.getElementById('supplier_id').value;
    const url = `{{ url_for('financial.export_report', report_type='payables_aging') }}?aging_date=${agingDate}&supplier_id=${supplierId}`;
    window.open(url, '_blank');
}

function resetForm() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('aging_date').value = today;
    document.getElementById('supplier_id').value = '';
    document.forms[0].submit();
}
</script>
{% endblock %}
