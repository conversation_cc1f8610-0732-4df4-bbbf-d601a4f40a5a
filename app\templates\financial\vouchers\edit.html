{% extends "financial/base.html" %}

{% block styles %}
    {{ super() }}
    <!-- 使用统一的用友主题样式 -->
{% endblock %}

{% block page_title %}编辑财务凭证{% endblock %}

{% block financial_css %}
<!-- 使用统一的用友主题样式，无需额外CSS -->
{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{{ url_for('financial.vouchers_index') }}">财务凭证管理</a></li>
<li class="breadcrumb-item active">编辑凭证</li>
{% endblock %}

{% block page_actions %}
<div class="uf-financial-actions">
    <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}" class="uf-btn uf-btn-sm">
        <i class="fas fa-eye"></i> 查看详情
    </a>
    <a href="{{ url_for('financial.vouchers_index') }}" class="uf-btn uf-btn-sm">
        <i class="fas fa-arrow-left"></i> 返回列表
    </a>
</div>
{% endblock %}

{% block financial_content %}
<div class="voucher-edit-container">
    <!-- 凭证表头 -->
    <div class="voucher-header-section">
        <div class="voucher-header-title">
            <h4>记账凭证</h4>
            <span class="uf-status-badge uf-status-{{ 'draft' if voucher.status == '草稿' else 'pending' if voucher.status == '待审核' else 'approved' if voucher.status == '已审核' else 'posted' }}">
                {{ voucher.status }}
            </span>
        </div>

        <div class="voucher-header-form">
            <div class="row align-items-center">
                <div class="col-auto">
                    <label class="form-label">字：</label>
                    <select class="form-control" id="voucherType" style="width: 80px;">
                        <option value="记" {{ 'selected' if voucher.voucher_type == '记' else '' }}>记</option>
                        <option value="收" {{ 'selected' if voucher.voucher_type == '收' else '' }}>收</option>
                        <option value="付" {{ 'selected' if voucher.voucher_type == '付' else '' }}>付</option>
                    </select>
                </div>
                <div class="col-auto">
                    <label class="form-label">号：</label>
                    <input type="text" class="form-control"
                           value="{{ voucher.voucher_number.split('PZ')[-1] if 'PZ' in voucher.voucher_number else voucher.voucher_number }}"
                           readonly style="width: 80px;">
                </div>
                <div class="col-auto">
                    <label class="form-label">日期：</label>
                    <input type="date" class="form-control"
                           id="voucherDate" value="{{ voucher.voucher_date }}" style="width: 150px;">
                </div>
                <div class="col-auto">
                    <label class="form-label">期：</label>
                    <input type="text" class="form-control"
                           id="voucherPeriod" value="{{ voucher.voucher_date.strftime('%m') if voucher.voucher_date else '' }}"
                           style="width: 60px;">
                </div>
                <div class="col-auto">
                    <label class="form-label">附单据：</label>
                    <input type="number" class="form-control"
                           id="attachmentCount" value="{{ voucher.attachment_count or 0 }}"
                           min="0" style="width: 60px;">
                    <span style="margin-left: 4px;">张</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 凭证明细表格 -->
    <div class="voucher-table-section">
        <table class="voucher-table">
            <thead>
                <tr>
                    <th width="18%">摘要</th>
                    <th width="28%">会计科目</th>
                    <th width="20%">
                        <div>借方金额</div>
                        <div class="amount-units">
                            <span>千</span><span>百</span><span>十</span><span>万</span><span>千</span><span>百</span><span>十</span><span>元</span><span>角</span><span>分</span>
                        </div>
                    </th>
                    <th width="20%">
                        <div>贷方金额</div>
                        <div class="amount-units">
                            <span>千</span><span>百</span><span>十</span><span>万</span><span>千</span><span>百</span><span>十</span><span>元</span><span>角</span><span>分</span>
                        </div>
                    </th>
                    <th width="14%">操作</th>
                </tr>
            </thead>
            <tbody id="voucher-rows">
                {% for detail in details %}
                <tr data-detail-id="{{ detail.id }}">
                    <td>
                        <input type="text" class="form-control summary-input" 
                               value="{{ detail.summary }}" readonly>
                    </td>
                    <td>
                        <input type="text" class="form-control subject-input" 
                               value="{{ detail.subject.code }} - {{ detail.subject.name }}" readonly>
                        <input type="hidden" class="subject-id" value="{{ detail.subject.id }}">
                    </td>
                    <td>
                        <input type="text" class="form-control debit-input amount-input"
                               value="{% if detail.debit_amount > 0 %}{{ '{:010.0f}'.format(detail.debit_amount * 100) }}{% endif %}"
                               readonly placeholder="0000000000">
                    </td>
                    <td>
                        <input type="text" class="form-control credit-input amount-input"
                               value="{% if detail.credit_amount > 0 %}{{ '{:010.0f}'.format(detail.credit_amount * 100) }}{% endif %}"
                               readonly placeholder="0000000000">
                    </td>
                    <td>
                        <div class="uf-action-buttons">
                            <button type="button" class="uf-action-btn uf-action-edit edit-row-btn" data-detail-id="{{ detail.id }}" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="uf-action-btn uf-action-delete delete-row-btn" data-detail-id="{{ detail.id }}" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr class="total-row">
                    <td colspan="2"><strong>合计：</strong></td>
                    <td><strong id="debit-total">0000000000</strong></td>
                    <td><strong id="credit-total">0000000000</strong></td>
                    <td></td>
                </tr>
                <tr class="amount-words-row">
                    <td colspan="5" id="amount-in-words"><strong>合计大写：</strong>零元整</td>
                </tr>
            </tfoot>
        </table>
        <div class="voucher-actions">
            <button type="button" class="btn btn-success" id="add-row-btn">
                <i class="fas fa-plus"></i> 添加分录
            </button>
            <button type="button" class="btn btn-primary" id="save-voucher-btn">
                <i class="fas fa-save"></i> 保存凭证
            </button>
            {% if voucher.status == '草稿' %}
            <button type="button" class="btn btn-warning" id="submit-review-btn">
                <i class="fas fa-paper-plane"></i> 提交审核
            </button>
            {% endif %}
            <button type="button" class="btn" id="check-balance-btn">
                <i class="fas fa-balance-scale"></i> 检查平衡
            </button>
            <button type="button" class="btn" id="print-voucher-btn">
                <i class="fas fa-print"></i> 打印凭证
            </button>
        </div>
    </div>

    <!-- 签字区域 -->
    <div class="voucher-signature-section">
        <div class="signature-row">
            <div class="signature-item">
                <span class="signature-label">审核：</span>
                <span class="signature-box">{{ voucher.reviewed_by.username if voucher.reviewed_by else '' }}</span>
            </div>
            <div class="signature-item">
                <span class="signature-label">记账：</span>
                <span class="signature-box">{{ voucher.posted_by.username if voucher.posted_by else '' }}</span>
            </div>
            <div class="signature-item">
                <span class="signature-label">出纳：</span>
                <span class="signature-box"></span>
            </div>
            <div class="signature-item">
                <span class="signature-label">制单：</span>
                <span class="signature-box">{{ voucher.created_by.username if voucher.created_by else '' }}</span>
            </div>
        </div>
    </div>
</div>

<!-- 会计科目选择器弹窗 -->
<div class="modal fade" id="subject-tree-modal" tabindex="-1" role="dialog" aria-labelledby="subjectTreeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header" style="background: #F2F4F6; border-bottom: 1px solid #DDDDDD;">
                <h5 class="modal-title" id="subjectTreeModalLabel" style="font-size: 14px; color: #333333;">选择会计科目</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="padding: 15px;">
                <div id="subject-tree-container"></div>
            </div>
            <div class="modal-footer" style="background: #F8F9FA; border-top: 1px solid #DDDDDD;">
                <button type="button" class="uf-btn" data-dismiss="modal">取消</button>
                <button type="button" class="uf-btn uf-btn-primary" id="confirm-subject-btn">确认选择</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<!-- 引入专用的JS文件 -->
<script src="{{ url_for('static', filename='financial/js/voucher-edit.js') }}"></script>

<script>
// 页面初始化
let voucherEditor;

document.addEventListener('DOMContentLoaded', function() {
    // 初始化凭证编辑器
    voucherEditor = new VoucherEditor({{ voucher.id }});
    console.log('凭证编辑页面初始化完成');

    // 绑定确认科目选择按钮事件
    const confirmSubjectBtn = document.getElementById('confirm-subject-btn');
    if (confirmSubjectBtn) {
        confirmSubjectBtn.addEventListener('click', function() {
            voucherEditor.confirmSubjectSelection();
        });
    }
});
</script>
{% endblock %}
