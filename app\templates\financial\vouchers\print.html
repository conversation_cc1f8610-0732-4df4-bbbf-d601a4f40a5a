<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财务凭证打印 - {{ voucher.voucher_number }}</title>

    <!-- 使用统一的用友主题样式（包含打印样式） -->
    <link rel="stylesheet" href="{{ url_for('static', filename='financial/css/yonyou-theme.css') }}">

    <style>
        /* 用友经典打印样式 */
        body {
            font-family: '宋体', 'SimSun', serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 0;
            background: white;
            color: #000000;
        }

        /* 打印页面设置 */
        @page {
            size: A4 landscape;
            margin: 10mm 15mm;
        }

        @media print {
            .no-print { display: none !important; }
            body { padding: 0; }
            .voucher-container {
                box-shadow: none;
                padding: 0;
                max-width: none;
                width: 100%;
            }
        }

        .voucher-container {
            max-width: 297mm;
            margin: 0 auto;
            background: white;
            padding: 15mm;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            position: relative;
        }

        /* 用友经典凭证头部 */
        .uf-print-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            border-bottom: 2px solid #000000;
            padding-bottom: 10px;
        }

        .uf-print-title {
            text-align: center;
            flex: 1;
        }

        .uf-print-title h1 {
            font-size: 22px;
            font-weight: bold;
            margin: 0 0 5px 0;
            letter-spacing: 2px;
        }

        .uf-print-title .company-name {
            font-size: 14px;
            color: #666666;
            margin: 0;
        }

        .uf-print-number {
            position: absolute;
            top: 15mm;
            right: 15mm;
            font-size: 14px;
            font-weight: bold;
        }
        
        .voucher-info-item .value {
            border-bottom: 1px solid #000;
            min-width: 120px;
            padding: 2px 5px;
        }
        
        .voucher-details {
            margin: 20px 0;
        }
        
        .voucher-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 12px;
        }
        
        .voucher-table th,
        .voucher-table td {
            border: 1px solid #000;
            padding: 8px 6px;
            text-align: left;
            vertical-align: middle;
        }
        
        .voucher-table th {
            background: #f5f5f5;
            font-weight: bold;
            text-align: center;
        }
        
        .voucher-table .amount {
            text-align: right;
            font-family: 'Arial', 'Times New Roman', monospace;
        }
        
        .voucher-table .total-row {
            background: #f0f0f0;
            font-weight: bold;
        }
        
        .voucher-signature {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ccc;
        }
        
        .signature-item {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 12px;
        }
        
        .signature-line {
            border-bottom: 1px solid #000;
            width: 100px;
            height: 20px;
            display: inline-block;
        }
        
        .print-controls {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .print-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
            font-size: 14px;
        }
        
        .print-btn:hover {
            background: #0056b3;
        }
        
        .print-btn.landscape {
            background: #28a745;
        }
        
        .print-btn.landscape:hover {
            background: #1e7e34;
        }
        
        /* 打印时隐藏控制按钮 */
        @media print {
            .print-controls {
                display: none !important;
            }
            
            .voucher-container {
                box-shadow: none;
                margin: 0;
                padding: 0;
                max-width: none;
            }
            
            @page {
                size: A4 landscape;
                margin: 15mm;
            }
        }
    </style>
</head>
<body class="voucher-print">
    <!-- 打印控制按钮 -->
    <div class="print-controls no-print">
        <button class="print-btn landscape" onclick="window.print()">
            <i class="fas fa-print"></i> 打印凭证（A4横向）
        </button>
        <button class="print-btn" onclick="window.close()">
            <i class="fas fa-times"></i> 关闭
        </button>
        <a href="{{ url_for('financial.export_voucher_pdf', voucher_id=voucher.id) }}" class="print-btn" style="text-decoration: none;">
            <i class="fas fa-file-pdf"></i> 导出PDF
        </a>
        <a href="{{ url_for('financial.export_voucher_excel', voucher_id=voucher.id) }}" class="print-btn" style="text-decoration: none;">
            <i class="fas fa-file-excel"></i> 导出Excel
        </a>
    </div>

    <!-- 用友风格凭证内容 -->
    <div class="voucher-container">
        <!-- 用友经典凭证头部 -->
        <div class="uf-print-header">
            <div class="uf-print-title">
                <h1>记账凭证</h1>
                <p class="company-name">{{ user_area.name }}</p>
            </div>
        </div>

        <!-- 凭证编号（右上角） -->
        <div class="uf-print-number">
            No.{{ voucher.voucher_number }}
        </div>

        <!-- 用友经典凭证信息 -->
        <div class="uf-print-info">
            <div class="uf-print-info-group">
                <div class="uf-print-info-item">
                    <label>字：</label>
                    <span class="value">{{ voucher.voucher_type }}</span>
                </div>
                <div class="uf-print-info-item">
                    <label>号：</label>
                    <span class="value">{{ voucher.voucher_number.split('PZ')[-1] if 'PZ' in voucher.voucher_number else voucher.voucher_number }}</span>
                </div>
            </div>
            <div class="uf-print-info-group">
                <div class="uf-print-info-item">
                    <label>日期：</label>
                    <span class="value">{{ voucher.voucher_date.strftime('%Y年%m月%d日') }}</span>
                </div>
                <div class="uf-print-info-item">
                    <label>附件：</label>
                    <span class="value">{{ voucher.attachment_count or 0 }}张</span>
                </div>
            </div>
        </div>

        <!-- 用友经典凭证明细表格 -->
        <table class="uf-print-table">
            <thead>
                <tr>
                    <th class="line-number">行号</th>
                    <th class="summary-cell">摘要</th>
                    <th class="subject-cell">会计科目</th>
                    <th class="amount-cell">借方金额</th>
                    <th class="amount-cell">贷方金额</th>
                </tr>
            </thead>
            <tbody>
                {% set total_debit = 0 %}
                {% set total_credit = 0 %}
                {% for detail in details %}
                <tr>
                    <td class="line-number">{{ detail.line_number }}</td>
                    <td class="summary-cell">{{ detail.summary or voucher.summary }}</td>
                    <td class="subject-cell">{{ detail.accounting_subject.code }} - {{ detail.accounting_subject.name if detail.accounting_subject else '未知科目' }}</td>
                    <td class="amount-cell">
                        {% if detail.debit_amount > 0 %}
                            {{ "{:,.2f}".format(detail.debit_amount|float) }}
                            {% set total_debit = total_debit + detail.debit_amount %}
                        {% endif %}
                    </td>
                    <td class="amount-cell">
                        {% if detail.credit_amount > 0 %}
                            {{ "{:,.2f}".format(detail.credit_amount|float) }}
                            {% set total_credit = total_credit + detail.credit_amount %}
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}

                <!-- 合计行 -->
                <tr class="total-row">
                    <td colspan="3" style="text-align: right; font-weight: bold;">合计：</td>
                    <td class="amount-cell">{{ "{:,.2f}".format(total_debit|float) }}</td>
                    <td class="amount-cell">{{ "{:,.2f}".format(total_credit|float) }}</td>
                </tr>

                <!-- 金额大写行 -->
                <tr class="amount-words-row">
                    <td colspan="5">
                        金额大写：人民币{{ "{:,.2f}".format(total_debit|float) }}元整
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- 用友经典签名栏 -->
        <div class="uf-print-signature">
            <div class="uf-print-signature-item">
                <div class="uf-print-signature-label">制单</div>
                <div class="uf-print-signature-line">
                    {% if voucher.creator %}{{ voucher.creator.username }}{% endif %}
                </div>
            </div>
            <div class="uf-print-signature-item">
                <div class="uf-print-signature-label">审核</div>
                <div class="uf-print-signature-line">
                    {% if voucher.reviewer %}{{ voucher.reviewer.username }}{% endif %}
                </div>
            </div>
            <div class="uf-print-signature-item">
                <div class="uf-print-signature-label">记账</div>
                <div class="uf-print-signature-line"></div>
            </div>
            <div class="uf-print-signature-item">
                <div class="uf-print-signature-label">出纳</div>
                <div class="uf-print-signature-line"></div>
            </div>
        </div>

        <!-- 打印信息 -->
        <div style="margin-top: 20px; text-align: right; font-size: 11px; color: #666666;">
            打印时间：{{ moment().format('YYYY-MM-DD HH:mm:ss') if moment else '' }}
            {% if not moment %}
            <script>document.write(new Date().toLocaleString());</script>
            {% endif %}
        </div>
    </div>

    <script>
        // 自动聚焦到打印按钮
        document.addEventListener('DOMContentLoaded', function() {
            // 如果是通过打印链接打开的，自动打印
            if (window.location.search.includes('auto_print=1')) {
                setTimeout(function() {
                    window.print();
                }, 500);
            }
        });
        
        // 打印后关闭窗口
        window.addEventListener('afterprint', function() {
            if (window.location.search.includes('auto_print=1')) {
                setTimeout(function() {
                    window.close();
                }, 1000);
            }
        });
    </script>
</body>
</html>
