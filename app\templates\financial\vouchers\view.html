{% extends "financial/base.html" %}

{% block title %}查看财务凭证{% endblock %}

{% block financial_content %}
<div class="voucher-edit-container">
    <!-- 凭证表头 -->
    <div class="voucher-header-section">
        <div class="voucher-header-title">
            <h4>财务凭证详情</h4>
            <span class="uf-status-badge uf-status-{{ 'draft' if voucher.status == '草稿' else 'pending' if voucher.status == '待审核' else 'approved' if voucher.status == '已审核' else 'posted' }}">
                {{ voucher.status }}
            </span>
        </div>

        <!-- 状态流转图 -->
        <div class="uf-status-flow">
            <div class="uf-status-flow-item draft {{ 'active' if voucher.status == '草稿' else 'completed' if voucher.status in ['待审核', '已审核', '已记账'] else '' }}">
                <div class="uf-status-flow-icon">📝</div>
                <div class="uf-status-flow-label">草稿</div>
                <div class="uf-status-flow-arrow">→</div>
            </div>
            <div class="uf-status-flow-item pending {{ 'active' if voucher.status == '待审核' else 'completed' if voucher.status in ['已审核', '已记账'] else '' }}">
                <div class="uf-status-flow-icon">⏳</div>
                <div class="uf-status-flow-label">待审核</div>
                <div class="uf-status-flow-arrow">→</div>
            </div>
            <div class="uf-status-flow-item approved {{ 'active' if voucher.status == '已审核' else 'completed' if voucher.status == '已记账' else '' }}">
                <div class="uf-status-flow-icon">✅</div>
                <div class="uf-status-flow-label">已审核</div>
                <div class="uf-status-flow-arrow">→</div>
            </div>
            <div class="uf-status-flow-item posted {{ 'active' if voucher.status == '已记账' else '' }}">
                <div class="uf-status-flow-icon">📚</div>
                <div class="uf-status-flow-label">已记账</div>
            </div>
        </div>

        <!-- 状态操作按钮 -->
        <div class="uf-status-actions">
            {% if voucher.status == '草稿' %}
            <button type="button" class="uf-status-action-btn uf-status-action-submit" onclick="submitVoucherForReview({{ voucher.id }})">
                <i class="fas fa-paper-plane"></i> 提交审核
            </button>
            {% endif %}

            {% if voucher.status == '待审核' %}
            <form method="POST" action="{{ url_for('financial.review_voucher', id=voucher.id) }}"
                  style="display: inline;" onsubmit="return confirm('确定要审核通过此凭证吗？')">
                <button type="submit" class="uf-status-action-btn uf-status-action-approve">
                    <i class="fas fa-check"></i> 审核通过
                </button>
            </form>
            <button type="button" class="uf-status-action-btn uf-status-action-reject" onclick="showRejectModal({{ voucher.id }})">
                <i class="fas fa-times"></i> 审核拒绝
            </button>
            {% endif %}

            {% if voucher.status in ['草稿', '待审核'] %}
            <a href="{{ url_for('financial.edit_voucher_professional', id=voucher.id) }}" class="uf-status-action-btn">
                <i class="fas fa-edit"></i> 编辑凭证
            </a>
            {% endif %}

            <a href="{{ url_for('financial.voucher_text_view', id=voucher.id) }}" class="uf-status-action-btn">
                <i class="fas fa-file-alt"></i> 文本视图
            </a>

            <a href="{{ url_for('financial.vouchers_index') }}" class="uf-status-action-btn">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>
    <!-- 凭证基本信息 -->
    <div class="voucher-table-section">
        <div class="voucher-header-form">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">凭证号：</label>
                    <span class="uf-voucher-number">{{ voucher.voucher_number }}</span>
                </div>
                <div class="col-md-3">
                    <label class="form-label">日期：</label>
                    <span class="uf-financial-date">{{ voucher.voucher_date.strftime('%Y-%m-%d') }}</span>
                </div>
                <div class="col-md-3">
                    <label class="form-label">类型：</label>
                    <span>{{ voucher.voucher_type }}</span>
                </div>
                <div class="col-md-3">
                    <label class="form-label">总金额：</label>
                    <span class="uf-amount-cell">¥{{ "{:,.2f}".format(voucher.total_amount) }}</span>
                </div>
            </div>
            <div class="row" style="margin-top: 8px;">
                <div class="col-md-3">
                    <label class="form-label">创建人：</label>
                    <span>{{ voucher.creator.username if voucher.creator else '未知' }}</span>
                </div>
                <div class="col-md-3">
                    <label class="form-label">创建时间：</label>
                    <span class="uf-financial-date">{{ voucher.created_at.strftime('%Y-%m-%d %H:%M') if voucher.created_at else '未知' }}</span>
                </div>
                {% if voucher.reviewed_by %}
                <div class="col-md-3">
                    <label class="form-label">审核人：</label>
                    <span>{{ voucher.reviewer.username if voucher.reviewer else '未知' }}</span>
                </div>
                <div class="col-md-3">
                    <label class="form-label">审核时间：</label>
                    <span class="uf-financial-date">{{ voucher.reviewed_at.strftime('%Y-%m-%d %H:%M') if voucher.reviewed_at else '未知' }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- 摘要信息 -->
        {% if voucher.summary %}
        <div style="margin-top: 15px;">
            <label class="form-label">摘要：</label>
            <div class="uf-summary-box">{{ voucher.summary }}</div>
        </div>
        {% endif %}

        <!-- 凭证明细表格 -->
        <div style="margin-top: 20px;">
            <h5 style="margin-bottom: 10px; color: #333333; font-size: 14px;">凭证明细</h5>
            {% if details %}
            <table class="voucher-table">
                <thead>
                    <tr>
                        <th style="width: 8%;">行号</th>
                        <th style="width: 25%;">会计科目</th>
                        <th style="width: 35%;">摘要</th>
                        <th style="width: 16%;">借方金额</th>
                        <th style="width: 16%;">贷方金额</th>
                    </tr>
                </thead>
                <tbody>
                    {% for detail in details %}
                    <tr>
                        <td style="text-align: center;">{{ detail.line_number }}</td>
                        <td>{{ detail.accounting_subject.code }} - {{ detail.accounting_subject.name if detail.accounting_subject else '未知科目' }}</td>
                        <td>{{ detail.summary or '' }}</td>
                        <td class="amount-input" style="text-align: right;">
                            {% if detail.debit_amount > 0 %}{{ "{:,.2f}".format(detail.debit_amount) }}{% endif %}
                        </td>
                        <td class="amount-input" style="text-align: right;">
                            {% if detail.credit_amount > 0 %}{{ "{:,.2f}".format(detail.credit_amount) }}{% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                    <tr class="total-row">
                        <td colspan="3" style="text-align: right; font-weight: bold;">合计：</td>
                        <td class="amount-input" style="text-align: right; font-weight: bold;">{{ "{:,.2f}".format(details|sum(attribute='debit_amount')) }}</td>
                        <td class="amount-input" style="text-align: right; font-weight: bold;">{{ "{:,.2f}".format(details|sum(attribute='credit_amount')) }}</td>
                    </tr>
                    <tr class="amount-words-row">
                        <td colspan="5" style="text-align: center;">
                            金额大写：{{ voucher.total_amount|amount_to_chinese if voucher.total_amount else '零元整' }}
                        </td>
                    </tr>
                </tbody>
            </table>
            {% else %}
            <div class="uf-table-empty">
                <i class="fas fa-file-invoice"></i>
                暂无凭证明细
            </div>
            {% endif %}
        </div>

        <!-- 备注信息 -->
        {% if voucher.notes %}
        <div style="margin-top: 15px;">
            <label class="form-label">备注：</label>
            <div class="uf-summary-box">{{ voucher.notes }}</div>
        </div>
        {% endif %}
    </div>

    <!-- 状态历史记录 -->
    <div class="uf-status-history">
        <div class="uf-status-history-header">
            <i class="fas fa-history"></i> 状态变更历史
        </div>
        <div class="uf-status-history-item">
            <div class="uf-status-history-icon" style="background: #F5F5F5; color: #666666;">📝</div>
            <div class="uf-status-history-content">
                <div class="uf-status-history-title">凭证创建</div>
                <div class="uf-status-history-desc">
                    由 {{ voucher.creator.username if voucher.creator else '未知' }} 创建凭证
                </div>
            </div>
            <div class="uf-status-history-time">
                {{ voucher.created_at.strftime('%Y-%m-%d %H:%M') if voucher.created_at else '未知' }}
            </div>
        </div>

        {% if voucher.status in ['待审核', '已审核', '已记账'] %}
        <div class="uf-status-history-item">
            <div class="uf-status-history-icon" style="background: #FFF8E1; color: #E65100;">⏳</div>
            <div class="uf-status-history-content">
                <div class="uf-status-history-title">提交审核</div>
                <div class="uf-status-history-desc">凭证已提交审核</div>
            </div>
            <div class="uf-status-history-time">
                {{ voucher.updated_at.strftime('%Y-%m-%d %H:%M') if voucher.updated_at else '未知' }}
            </div>
        </div>
        {% endif %}

        {% if voucher.reviewed_by and voucher.status in ['已审核', '已记账'] %}
        <div class="uf-status-history-item">
            <div class="uf-status-history-icon" style="background: #E8F5E8; color: #1B5E20;">✅</div>
            <div class="uf-status-history-content">
                <div class="uf-status-history-title">审核通过</div>
                <div class="uf-status-history-desc">
                    由 {{ voucher.reviewer.username if voucher.reviewer else '未知' }} 审核通过
                </div>
            </div>
            <div class="uf-status-history-time">
                {{ voucher.reviewed_at.strftime('%Y-%m-%d %H:%M') if voucher.reviewed_at else '未知' }}
            </div>
        </div>
        {% endif %}

        {% if voucher.status == '已记账' %}
        <div class="uf-status-history-item">
            <div class="uf-status-history-icon" style="background: #E3F2FD; color: #0D47A1;">📚</div>
            <div class="uf-status-history-content">
                <div class="uf-status-history-title">记账完成</div>
                <div class="uf-status-history-desc">凭证已记账</div>
            </div>
            <div class="uf-status-history-time">
                {{ voucher.updated_at.strftime('%Y-%m-%d %H:%M') if voucher.updated_at else '未知' }}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 拒绝审核模态框 -->
<div class="modal fade" id="rejectVoucherModal" tabindex="-1" role="dialog" aria-labelledby="rejectVoucherModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header" style="background: #F2F4F6; border-bottom: 1px solid #DDDDDD;">
                <h5 class="modal-title" id="rejectVoucherModalLabel" style="font-size: 14px; color: #333333;">拒绝审核凭证</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="rejectVoucherForm" method="POST">
                <div class="modal-body" style="padding: 15px;">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        确定要拒绝审核此凭证吗？凭证将退回到草稿状态。
                    </div>
                    <div class="form-group">
                        <label for="rejectReason" class="form-label">拒绝原因：</label>
                        <textarea class="form-control" name="reject_reason" id="rejectReason" rows="3" placeholder="请填写拒绝原因..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer" style="background: #F8F9FA; border-top: 1px solid #DDDDDD;">
                    <button type="button" class="uf-btn" data-dismiss="modal">取消</button>
                    <button type="submit" class="uf-btn uf-status-action-reject">确认拒绝</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
// 提交凭证审核
function submitVoucherForReview(voucherId) {
    if (confirm('确定要提交此凭证审核吗？')) {
        fetch(`/financial/vouchers/${voucherId}/submit-review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('凭证已提交审核');
                window.location.reload();
            } else {
                alert('提交失败：' + data.message);
            }
        })
        .catch(error => {
            alert('提交失败，请重试');
        });
    }
}

// 显示拒绝审核模态框
function showRejectModal(voucherId) {
    const form = document.getElementById('rejectVoucherForm');
    form.action = `/financial/vouchers/${voucherId}/reject`;
    document.getElementById('rejectReason').value = '';
    $('#rejectVoucherModal').modal('show');
}
</script>
{% endblock %}
