!function(o,n){for(var e in n)o[e]=n[e]}(window,function(o){var n={};function e(t){if(n[t])return n[t].exports;var r=n[t]={i:t,l:!1,exports:{}};return o[t].call(r.exports,r,r.exports,e),r.l=!0,r.exports}return e.m=o,e.c=n,e.d=function(o,n,t){e.o(o,n)||Object.defineProperty(o,n,{enumerable:!0,get:t})},e.r=function(o){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(o,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(o,"__esModule",{value:!0})},e.t=function(o,n){if(1&n&&(o=e(o)),8&n)return o;if(4&n&&"object"==typeof o&&o&&o.__esModule)return o;var t=Object.create(null);if(e.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:o}),2&n&&"string"!=typeof o)for(var r in o)e.d(t,r,function(n){return o[n]}.bind(null,r));return t},e.n=function(o){var n=o&&o.__esModule?function(){return o.default}:function(){return o};return e.d(n,"a",n),n},e.o=function(o,n){return Object.prototype.hasOwnProperty.call(o,n)},e.p="http://service.chanjet.com/robot_static/js/",e(e.s=106)}({106:function(o,n){var e;window.Command_Login=function(){window.parent.postMessage({methods:"login"},"*"),console.log("Command_Login click")},window.Command_Min=function(){window.parent.postMessage({methods:"min"},"*"),console.log("Command_Min click")},window.Command_Shut=function(){window.parent.postMessage({methods:"shut"},"*"),console.log("Command_Shut click")},window.Command_OpenLink=function(o,n){console.log("Command_OpenLink click: "+o+"-"+n),window.open(o)},window.Command_Static=function(){console.log("Command_Static click")},window.Command_Udesk=function(){window.parent.postMessage({methods:"im"},"*")},window.Command_Udesk_CBB=function(){window.parent.postMessage({methods:"im_cbb"},"*")},window.Command_OpenProductLink=function(o){window.parent.postMessage({methods:"open_product_link",url:o},"*")},window.Command_Report=function(o){window.parent.postMessage({methods:"report",code:o},"*"),console.log("Command_Report click")},window.Command_NewMsg=function(){window.parent.postMessage({methods:"newMsg"},"*")},window.hkjinfo={token:null,orgid:null},window.App={};var t=function(){return{frame_userinfo:ng_robot.actions.setUserData,frame_study:ng_robot.actions.setStudy,frame_render:ng_robot.actions.invoke,frame_report:ng_robot.actions.report,frame_invoke_send:ng_robot.actions.invokeSend,frame_hkjinfo:function(o){window.hkjinfo=o},productOperation_frame_appInfo:function(o){console.log("productOperation_frame_appInfo==",o),window.App=o},growing_track:function(o){document.getElementById("shuru").focus(),window.gio&&gio("track","fubao_open",{fubao_open_from:o})},frame_url:function(o){var n="push"==o.type?"/message/push":"/message/system";ng_robot.actions.setUrl(n)},frame_link:function(o){var n="https://service.chanjet.com/";"workerorder"==o.type&&(n+="partner/workorder/detail?id="+o.work_no),"at"!=o.type&&"comment"!=o.type||(n+="message/"+o.message_id),ng_robot.actions.stopA(n)},frame_msg:ng_robot.actions.setUrl}};window.addEventListener("load",(function(){window.addEventListener("message",(function(o){e=e||t();var n=o.data;e[n.method]&&e[n.method](n.data)}))}))}}));