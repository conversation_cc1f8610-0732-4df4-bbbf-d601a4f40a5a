!function(t,e){t.DOMPurify=e()}(this,function(){"use strict";function t(e){"@babel/helpers - typeof";return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function e(t,n){return(e=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,n)}function n(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}function o(t,r,i){return o=n()?Reflect.construct:function(t,n,o){var r=[null];r.push.apply(r,n);var i=Function.bind.apply(t,r),a=new i;return o&&e(a,o.prototype),a},o.apply(null,arguments)}function r(t){return i(t)||a(t)||s(t)||l()}function i(t){if(Array.isArray(t))return c(t)}function a(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function s(t,e){if(t){if("string"==typeof t)return c(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(t,e):void 0}}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}function l(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function u(t){return function(e){for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return S(t,e,o)}}function p(t){return function(){for(var e=arguments.length,n=new Array(e),o=0;o<e;o++)n[o]=arguments[o];return T(t,n)}}function f(t,e){y&&y(t,null);for(var n=e.length;n--;){var o=e[n];if("string"==typeof o){var r=O(o);r!==o&&(v(e)||(e[n]=r),o=r)}t[o]=!0}return t}function h(t){var e,n=k(null);for(e in t)S(g,t,[e])&&(n[e]=t[e]);return n}function d(t,e){function n(t){return null}for(;null!==t;){var o=_(t,e);if(o){if(o.get)return u(o.get);if("function"==typeof o.value)return u(o.value)}t=b(t)}return n}function m(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:nt(),n=function(t){return m(t)};if(n.version="2.3.8",n.removed=[],!e||!e.document||9!==e.document.nodeType)return n.isSupported=!1,n;var o=e.document,i=e.document,a=e.DocumentFragment,s=e.HTMLTemplateElement,c=e.Node,l=e.Element,u=e.NodeFilter,p=e.NamedNodeMap,g=void 0===p?e.NamedNodeMap||e.MozNamedAttrMap:p,y=e.HTMLFormElement,v=e.DOMParser,b=e.trustedTypes,_=l.prototype,x=d(_,"cloneNode"),k=d(_,"nextSibling"),E=d(_,"childNodes"),S=d(_,"parentNode");if("function"==typeof s){var T=i.createElement("template");T.content&&T.content.ownerDocument&&(i=T.content.ownerDocument)}var rt=ot(b,o),it=rt?rt.createHTML(""):"",at=i,st=at.implementation,ct=at.createNodeIterator,lt=at.createDocumentFragment,ut=at.getElementsByTagName,pt=o.importNode,ft={};try{ft=h(i).documentMode?i.documentMode:{}}catch(ht){}var dt={};n.isSupported="function"==typeof S&&st&&"undefined"!=typeof st.createHTMLDocument&&9!==ft;var mt,gt,yt=$,vt=G,bt=V,_t=K,wt=Q,xt=tt,kt=Z,Et=null,St=f({},[].concat(r(P),r(U),r(M),r(F),r(H))),Tt=null,Ct=f({},[].concat(r(W),r(J),r(X),r(Y))),At=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Nt=null,Ot=null,Rt=!0,Bt=!0,jt=!1,It=!1,Dt=!1,Lt=!1,Pt=!1,Ut=!1,Mt=!1,zt=!1,Ft=!0,qt=!0,Ht=!1,Wt={},Jt=null,Xt=f({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Yt=null,$t=f({},["audio","video","img","source","image","track"]),Gt=null,Vt=f({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Kt="http://www.w3.org/1998/Math/MathML",Zt="http://www.w3.org/2000/svg",Qt="http://www.w3.org/1999/xhtml",te=Qt,ee=!1,ne=["application/xhtml+xml","text/html"],oe="text/html",re=null,ie=i.createElement("form"),ae=function(t){return t instanceof RegExp||t instanceof Function},se=function(e){re&&re===e||(e&&"object"===t(e)||(e={}),e=h(e),Et="ALLOWED_TAGS"in e?f({},e.ALLOWED_TAGS):St,Tt="ALLOWED_ATTR"in e?f({},e.ALLOWED_ATTR):Ct,Gt="ADD_URI_SAFE_ATTR"in e?f(h(Vt),e.ADD_URI_SAFE_ATTR):Vt,Yt="ADD_DATA_URI_TAGS"in e?f(h($t),e.ADD_DATA_URI_TAGS):$t,Jt="FORBID_CONTENTS"in e?f({},e.FORBID_CONTENTS):Xt,Nt="FORBID_TAGS"in e?f({},e.FORBID_TAGS):{},Ot="FORBID_ATTR"in e?f({},e.FORBID_ATTR):{},Wt="USE_PROFILES"in e&&e.USE_PROFILES,Rt=e.ALLOW_ARIA_ATTR!==!1,Bt=e.ALLOW_DATA_ATTR!==!1,jt=e.ALLOW_UNKNOWN_PROTOCOLS||!1,It=e.SAFE_FOR_TEMPLATES||!1,Dt=e.WHOLE_DOCUMENT||!1,Ut=e.RETURN_DOM||!1,Mt=e.RETURN_DOM_FRAGMENT||!1,zt=e.RETURN_TRUSTED_TYPE||!1,Pt=e.FORCE_BODY||!1,Ft=e.SANITIZE_DOM!==!1,qt=e.KEEP_CONTENT!==!1,Ht=e.IN_PLACE||!1,kt=e.ALLOWED_URI_REGEXP||kt,te=e.NAMESPACE||Qt,e.CUSTOM_ELEMENT_HANDLING&&ae(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(At.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ae(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(At.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(At.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),mt=mt=ne.indexOf(e.PARSER_MEDIA_TYPE)===-1?oe:e.PARSER_MEDIA_TYPE,gt="application/xhtml+xml"===mt?function(t){return t}:O,It&&(Bt=!1),Mt&&(Ut=!0),Wt&&(Et=f({},r(H)),Tt=[],Wt.html===!0&&(f(Et,P),f(Tt,W)),Wt.svg===!0&&(f(Et,U),f(Tt,J),f(Tt,Y)),Wt.svgFilters===!0&&(f(Et,M),f(Tt,J),f(Tt,Y)),Wt.mathMl===!0&&(f(Et,F),f(Tt,X),f(Tt,Y))),e.ADD_TAGS&&(Et===St&&(Et=h(Et)),f(Et,e.ADD_TAGS)),e.ADD_ATTR&&(Tt===Ct&&(Tt=h(Tt)),f(Tt,e.ADD_ATTR)),e.ADD_URI_SAFE_ATTR&&f(Gt,e.ADD_URI_SAFE_ATTR),e.FORBID_CONTENTS&&(Jt===Xt&&(Jt=h(Jt)),f(Jt,e.FORBID_CONTENTS)),qt&&(Et["#text"]=!0),Dt&&f(Et,["html","head","body"]),Et.table&&(f(Et,["tbody"]),delete Nt.tbody),w&&w(e),re=e)},ce=f({},["mi","mo","mn","ms","mtext"]),le=f({},["foreignobject","desc","title","annotation-xml"]),ue=f({},["title","style","font","a","script"]),pe=f({},U);f(pe,M),f(pe,z);var fe=f({},F);f(fe,q);var he=function(t){var e=S(t);e&&e.tagName||(e={namespaceURI:Qt,tagName:"template"});var n=O(t.tagName),o=O(e.tagName);return t.namespaceURI===Zt?e.namespaceURI===Qt?"svg"===n:e.namespaceURI===Kt?"svg"===n&&("annotation-xml"===o||ce[o]):Boolean(pe[n]):t.namespaceURI===Kt?e.namespaceURI===Qt?"math"===n:e.namespaceURI===Zt?"math"===n&&le[o]:Boolean(fe[n]):t.namespaceURI===Qt&&(!(e.namespaceURI===Zt&&!le[o])&&(!(e.namespaceURI===Kt&&!ce[o])&&(!fe[n]&&(ue[n]||!pe[n]))))},de=function(t){N(n.removed,{element:t});try{t.parentNode.removeChild(t)}catch(e){try{t.outerHTML=it}catch(e){t.remove()}}},me=function(t,e){try{N(n.removed,{attribute:e.getAttributeNode(t),from:e})}catch(o){N(n.removed,{attribute:null,from:e})}if(e.removeAttribute(t),"is"===t&&!Tt[t])if(Ut||Mt)try{de(e)}catch(o){}else try{e.setAttribute(t,"")}catch(o){}},ge=function(t){var e,n;if(Pt)t="<remove></remove>"+t;else{var o=R(t,/^[\r\n\t ]+/);n=o&&o[0]}"application/xhtml+xml"===mt&&(t='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+t+"</body></html>");var r=rt?rt.createHTML(t):t;if(te===Qt)try{e=(new v).parseFromString(r,mt)}catch(a){}if(!e||!e.documentElement){e=st.createDocument(te,"template",null);try{e.documentElement.innerHTML=ee?"":r}catch(a){}}var s=e.body||e.documentElement;return t&&n&&s.insertBefore(i.createTextNode(n),s.childNodes[0]||null),te===Qt?ut.call(e,Dt?"html":"body")[0]:Dt?e.documentElement:s},ye=function(t){return ct.call(t.ownerDocument||t,t,u.SHOW_ELEMENT|u.SHOW_COMMENT|u.SHOW_TEXT,null,!1)},ve=function(t){return t instanceof y&&("string"!=typeof t.nodeName||"string"!=typeof t.textContent||"function"!=typeof t.removeChild||!(t.attributes instanceof g)||"function"!=typeof t.removeAttribute||"function"!=typeof t.setAttribute||"string"!=typeof t.namespaceURI||"function"!=typeof t.insertBefore)},be=function(e){return"object"===t(c)?e instanceof c:e&&"object"===t(e)&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName},_e=function(t,e,o){dt[t]&&C(dt[t],function(t){t.call(n,e,o,re)})},we=function(t){var e;if(_e("beforeSanitizeElements",t,null),ve(t))return de(t),!0;if(D(/[\u0080-\uFFFF]/,t.nodeName))return de(t),!0;var o=gt(t.nodeName);if(_e("uponSanitizeElement",t,{tagName:o,allowedTags:Et}),t.hasChildNodes()&&!be(t.firstElementChild)&&(!be(t.content)||!be(t.content.firstElementChild))&&D(/<[/\w]/g,t.innerHTML)&&D(/<[/\w]/g,t.textContent))return de(t),!0;if("select"===o&&D(/<template/i,t.innerHTML))return de(t),!0;if(!Et[o]||Nt[o]){if(!Nt[o]&&ke(o)){if(At.tagNameCheck instanceof RegExp&&D(At.tagNameCheck,o))return!1;if(At.tagNameCheck instanceof Function&&At.tagNameCheck(o))return!1}if(qt&&!Jt[o]){var r=S(t)||t.parentNode,i=E(t)||t.childNodes;if(i&&r)for(var a=i.length,s=a-1;s>=0;--s)r.insertBefore(x(i[s],!0),k(t))}return de(t),!0}return t instanceof l&&!he(t)?(de(t),!0):"noscript"!==o&&"noembed"!==o||!D(/<\/no(script|embed)/i,t.innerHTML)?(It&&3===t.nodeType&&(e=t.textContent,e=B(e,yt," "),e=B(e,vt," "),t.textContent!==e&&(N(n.removed,{element:t.cloneNode()}),t.textContent=e)),_e("afterSanitizeElements",t,null),!1):(de(t),!0)},xe=function(t,e,n){if(Ft&&("id"===e||"name"===e)&&(n in i||n in ie))return!1;if(Bt&&!Ot[e]&&D(bt,e));else if(Rt&&D(_t,e));else if(!Tt[e]||Ot[e]){if(!(ke(t)&&(At.tagNameCheck instanceof RegExp&&D(At.tagNameCheck,t)||At.tagNameCheck instanceof Function&&At.tagNameCheck(t))&&(At.attributeNameCheck instanceof RegExp&&D(At.attributeNameCheck,e)||At.attributeNameCheck instanceof Function&&At.attributeNameCheck(e))||"is"===e&&At.allowCustomizedBuiltInElements&&(At.tagNameCheck instanceof RegExp&&D(At.tagNameCheck,n)||At.tagNameCheck instanceof Function&&At.tagNameCheck(n))))return!1}else if(Gt[e]);else if(D(kt,B(n,xt,"")));else if("src"!==e&&"xlink:href"!==e&&"href"!==e||"script"===t||0!==j(n,"data:")||!Yt[t]){if(jt&&!D(wt,B(n,xt,"")));else if(n)return!1}else;return!0},ke=function(t){return t.indexOf("-")>0},Ee=function(t){var e,o,r,i;_e("beforeSanitizeAttributes",t,null);var a=t.attributes;if(a){var s={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Tt};for(i=a.length;i--;){e=a[i];var c=e,l=c.name,u=c.namespaceURI;if(o="value"===l?e.value:I(e.value),r=gt(l),s.attrName=r,s.attrValue=o,s.keepAttr=!0,s.forceKeepAttr=void 0,_e("uponSanitizeAttribute",t,s),o=s.attrValue,!s.forceKeepAttr&&(me(l,t),s.keepAttr))if(D(/\/>/i,o))me(l,t);else{It&&(o=B(o,yt," "),o=B(o,vt," "));var p=gt(t.nodeName);if(xe(p,r,o))try{u?t.setAttributeNS(u,l,o):t.setAttribute(l,o),A(n.removed)}catch(f){}}}_e("afterSanitizeAttributes",t,null)}},Se=function Te(t){var e,n=ye(t);for(_e("beforeSanitizeShadowDOM",t,null);e=n.nextNode();)_e("uponSanitizeShadowNode",e,null),we(e)||(e.content instanceof a&&Te(e.content),Ee(e));_e("afterSanitizeShadowDOM",t,null)};return n.sanitize=function(r,i){var s,l,u,p,f;if(ee=!r,ee&&(r="<!-->"),"string"!=typeof r&&!be(r)){if("function"!=typeof r.toString)throw L("toString is not a function");if(r=r.toString(),"string"!=typeof r)throw L("dirty is not a string, aborting")}if(!n.isSupported){if("object"===t(e.toStaticHTML)||"function"==typeof e.toStaticHTML){if("string"==typeof r)return e.toStaticHTML(r);if(be(r))return e.toStaticHTML(r.outerHTML)}return r}if(Lt||se(i),n.removed=[],"string"==typeof r&&(Ht=!1),Ht){if(r.nodeName){var h=gt(r.nodeName);if(!Et[h]||Nt[h])throw L("root node is forbidden and cannot be sanitized in-place")}}else if(r instanceof c)s=ge("<!---->"),l=s.ownerDocument.importNode(r,!0),1===l.nodeType&&"BODY"===l.nodeName?s=l:"HTML"===l.nodeName?s=l:s.appendChild(l);else{if(!Ut&&!It&&!Dt&&r.indexOf("<")===-1)return rt&&zt?rt.createHTML(r):r;if(s=ge(r),!s)return Ut?null:zt?it:""}s&&Pt&&de(s.firstChild);for(var d=ye(Ht?r:s);u=d.nextNode();)3===u.nodeType&&u===p||we(u)||(u.content instanceof a&&Se(u.content),Ee(u),p=u);if(p=null,Ht)return r;if(Ut){if(Mt)for(f=lt.call(s.ownerDocument);s.firstChild;)f.appendChild(s.firstChild);else f=s;return Tt.shadowroot&&(f=pt.call(o,f,!0)),f}var m=Dt?s.outerHTML:s.innerHTML;return Dt&&Et["!doctype"]&&s.ownerDocument&&s.ownerDocument.doctype&&s.ownerDocument.doctype.name&&D(et,s.ownerDocument.doctype.name)&&(m="<!DOCTYPE "+s.ownerDocument.doctype.name+">\n"+m),It&&(m=B(m,yt," "),m=B(m,vt," ")),rt&&zt?rt.createHTML(m):m},n.setConfig=function(t){se(t),Lt=!0},n.clearConfig=function(){re=null,Lt=!1},n.isValidAttribute=function(t,e,n){re||se({});var o=gt(t),r=gt(e);return xe(o,r,n)},n.addHook=function(t,e){"function"==typeof e&&(dt[t]=dt[t]||[],N(dt[t],e))},n.removeHook=function(t){if(dt[t])return A(dt[t])},n.removeHooks=function(t){dt[t]&&(dt[t]=[])},n.removeAllHooks=function(){dt={}},n}var g=Object.hasOwnProperty,y=Object.setPrototypeOf,v=Object.isFrozen,b=Object.getPrototypeOf,_=Object.getOwnPropertyDescriptor,w=Object.freeze,x=Object.seal,k=Object.create,E="undefined"!=typeof Reflect&&Reflect,S=E.apply,T=E.construct;S||(S=function(t,e,n){return t.apply(e,n)}),w||(w=function(t){return t}),x||(x=function(t){return t}),T||(T=function(t,e){return o(t,r(e))});var C=u(Array.prototype.forEach),A=u(Array.prototype.pop),N=u(Array.prototype.push),O=u(String.prototype.toLowerCase),R=u(String.prototype.match),B=u(String.prototype.replace),j=u(String.prototype.indexOf),I=u(String.prototype.trim),D=u(RegExp.prototype.test),L=p(TypeError),P=w(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),U=w(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),M=w(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),z=w(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),F=w(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),q=w(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),H=w(["#text"]),W=w(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),J=w(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),X=w(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Y=w(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),$=x(/\{\{[\w\W]*|[\w\W]*\}\}/gm),G=x(/<%[\w\W]*|[\w\W]*%>/gm),V=x(/^data-[\-\w.\u00B7-\uFFFF]/),K=x(/^aria-[\-\w]+$/),Z=x(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Q=x(/^(?:\w+script|data):/i),tt=x(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),et=x(/^html$/i),nt=function(){return"undefined"==typeof window?null:window},ot=function(e,n){if("object"!==t(e)||"function"!=typeof e.createPolicy)return null;var o=null,r="data-tt-policy-suffix";n.currentScript&&n.currentScript.hasAttribute(r)&&(o=n.currentScript.getAttribute(r));var i="dompurify"+(o?"#"+o:"");try{return e.createPolicy(i,{createHTML:function(t){return t}})}catch(a){return null}},rt=m();return rt}),!function(t,e){t.__udeskIo=e()}(this,function(){var t;return function e(t,n,o){function r(a,s){if(!n[a]){if(!t[a]){var c="function"==typeof require&&require;if(!s&&c)return c(a,!0);if(i)return i(a,!0);throw new Error("Cannot find module '"+a+"'")}var l=n[a]={exports:{}};t[a][0].call(l.exports,function(e){var n=t[a][1][e];return r(n?n:e)},l,l.exports,e,t,n,o)}return n[a].exports}for(var i="function"==typeof require&&require,a=0;a<o.length;a++)r(o[a]);return r}({1:[function(t,e){e.exports=t("./lib/")},{"./lib/":2}],2:[function(t,e,n){function o(t,e){"object"==typeof t&&(e=t,t=void 0),e=e||{};var n,o=r(t),i=o.source,l=o.id;return e.forceNew||e["force new connection"]||!1===e.multiplex?(s("ignoring socket cache for %s",i),n=a(i,e)):(c[l]||(s("new io instance for %s",i),c[l]=a(i,e)),n=c[l]),n.socket(o.path)}var r=t("./url"),i=t("socket.io-parser"),a=t("./manager"),s=t("debug")("socket.io-client");e.exports=n=o;var c=n.managers={};n.protocol=i.protocol,n.connect=o,n.Manager=t("./manager"),n.Socket=t("./socket")},{"./manager":3,"./socket":5,"./url":6,debug:10,"socket.io-parser":44}],3:[function(t,e){function n(t,e){return this instanceof n?(t&&"object"==typeof t&&(e=t,t=void 0),e=e||{},e.path=e.path||"/socket.io",this.nsps={},this.subs=[],this.opts=e,this.reconnection(e.reconnection!==!1),this.reconnectionAttempts(e.reconnectionAttempts||1/0),this.reconnectionDelay(e.reconnectionDelay||1e3),this.reconnectionDelayMax(e.reconnectionDelayMax||5e3),this.randomizationFactor(e.randomizationFactor||.5),this.backoff=new p({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==e.timeout?2e4:e.timeout),this.readyState="closed",this.uri=t,this.connected=[],this.encoding=!1,this.packetBuffer=[],this.encoder=new a.Encoder,this.decoder=new a.Decoder,this.autoConnect=e.autoConnect!==!1,void(this.autoConnect&&this.open())):new n(t,e)}var o=(t("./url"),t("engine.io-client")),r=t("./socket"),i=t("component-emitter"),a=t("socket.io-parser"),s=t("./on"),c=t("component-bind"),l=(t("object-component"),t("debug")("socket.io-client:manager")),u=t("indexof"),p=t("backo2");e.exports=n,n.prototype.emitAll=function(){this.emit.apply(this,arguments);for(var t in this.nsps)this.nsps[t].emit.apply(this.nsps[t],arguments)},n.prototype.updateSocketIds=function(){for(var t in this.nsps)this.nsps[t].id=this.engine.id},i(n.prototype),n.prototype.reconnection=function(t){return arguments.length?(this._reconnection=!!t,this):this._reconnection},n.prototype.reconnectionAttempts=function(t){return arguments.length?(this._reconnectionAttempts=t,this):this._reconnectionAttempts},n.prototype.reconnectionDelay=function(t){return arguments.length?(this._reconnectionDelay=t,this.backoff&&this.backoff.setMin(t),this):this._reconnectionDelay},n.prototype.randomizationFactor=function(t){return arguments.length?(this._randomizationFactor=t,this.backoff&&this.backoff.setJitter(t),this):this._randomizationFactor},n.prototype.reconnectionDelayMax=function(t){return arguments.length?(this._reconnectionDelayMax=t,this.backoff&&this.backoff.setMax(t),this):this._reconnectionDelayMax},n.prototype.timeout=function(t){return arguments.length?(this._timeout=t,this):this._timeout},n.prototype.maybeReconnectOnOpen=function(){!this.reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()},n.prototype.open=n.prototype.connect=function(t){if(l("readyState %s",this.readyState),~this.readyState.indexOf("open"))return this;l("opening %s",this.uri),this.engine=o(this.uri,this.opts);var e=this.engine,n=this;this.readyState="opening",this.skipReconnect=!1;var r=s(e,"open",function(){n.onopen(),t&&t()}),i=s(e,"error",function(e){if(l("connect_error"),n.cleanup(),n.readyState="closed",n.emitAll("connect_error",e),t){var o=new Error("Connection error");o.data=e,t(o)}else n.maybeReconnectOnOpen()});if(!1!==this._timeout){var a=this._timeout;l("connect attempt will timeout after %d",a);var c=setTimeout(function(){l("connect attempt timed out after %d",a),r.destroy(),e.close(),e.emit("error","timeout"),n.emitAll("connect_timeout",a)},a);this.subs.push({destroy:function(){clearTimeout(c)}})}return this.subs.push(r),this.subs.push(i),this},n.prototype.onopen=function(){l("open"),this.cleanup(),this.readyState="open",this.emit("open");var t=this.engine;this.subs.push(s(t,"data",c(this,"ondata"))),this.subs.push(s(this.decoder,"decoded",c(this,"ondecoded"))),this.subs.push(s(t,"error",c(this,"onerror"))),this.subs.push(s(t,"close",c(this,"onclose")))},n.prototype.ondata=function(t){this.decoder.add(t)},n.prototype.ondecoded=function(t){this.emit("packet",t)},n.prototype.onerror=function(t){l("error",t),this.emitAll("error",t)},n.prototype.socket=function(t){var e=this.nsps[t];if(!e){e=new r(this,t),this.nsps[t]=e;var n=this;e.on("connect",function(){e.id=n.engine.id,~u(n.connected,e)||n.connected.push(e)})}return e},n.prototype.destroy=function(t){var e=u(this.connected,t);~e&&this.connected.splice(e,1),this.connected.length||this.close()},n.prototype.packet=function(t){l("writing packet %j",t);var e=this;e.encoding?e.packetBuffer.push(t):(e.encoding=!0,this.encoder.encode(t,function(t){for(var n=0;n<t.length;n++)e.engine.write(t[n]);e.encoding=!1,e.processPacketQueue()}))},n.prototype.processPacketQueue=function(){if(this.packetBuffer.length>0&&!this.encoding){var t=this.packetBuffer.shift();this.packet(t)}},n.prototype.cleanup=function(){for(var t;t=this.subs.shift();)t.destroy();this.packetBuffer=[],this.encoding=!1,this.decoder.destroy()},n.prototype.close=n.prototype.disconnect=function(){this.skipReconnect=!0,this.backoff.reset(),this.readyState="closed",this.engine&&this.engine.close()},n.prototype.onclose=function(t){l("close"),this.cleanup(),this.backoff.reset(),this.readyState="closed",this.emit("close",t),this._reconnection&&!this.skipReconnect&&this.reconnect()},n.prototype.reconnect=function(){if(this.reconnecting||this.skipReconnect)return this;var t=this;if(this.backoff.attempts>=this._reconnectionAttempts)l("reconnect failed"),this.backoff.reset(),this.emitAll("reconnect_failed"),this.reconnecting=!1;else{var e=this.backoff.duration();l("will wait %dms before reconnect attempt",e),this.reconnecting=!0;var n=setTimeout(function(){t.skipReconnect||(l("attempting reconnect"),t.emitAll("reconnect_attempt",t.backoff.attempts),t.emitAll("reconnecting",t.backoff.attempts),t.skipReconnect||t.open(function(e){e?(l("reconnect attempt error"),t.reconnecting=!1,t.reconnect(),t.emitAll("reconnect_error",e.data)):(l("reconnect success"),t.onreconnect())}))},e);this.subs.push({destroy:function(){clearTimeout(n)}})}},n.prototype.onreconnect=function(){var t=this.backoff.attempts;this.reconnecting=!1,this.backoff.reset(),this.updateSocketIds(),this.emitAll("reconnect",t)}},{"./on":4,"./socket":5,"./url":6,backo2:7,"component-bind":8,"component-emitter":9,debug:10,"engine.io-client":11,indexof:40,"object-component":41,"socket.io-parser":44}],4:[function(t,e){function n(t,e,n){return t.on(e,n),{destroy:function(){t.removeListener(e,n)}}}e.exports=n},{}],5:[function(t,e,n){function o(t,e){this.io=t,this.nsp=e,this.json=this,this.ids=0,this.acks={},this.io.autoConnect&&this.open(),this.receiveBuffer=[],this.sendBuffer=[],this.connected=!1,this.disconnected=!0}var r=t("socket.io-parser"),i=t("component-emitter"),a=t("to-array"),s=t("./on"),c=t("component-bind"),l=t("debug")("socket.io-client:socket"),u=t("has-binary");e.exports=n=o;var p={connect:1,connect_error:1,connect_timeout:1,disconnect:1,error:1,reconnect:1,reconnect_attempt:1,reconnect_failed:1,reconnect_error:1,reconnecting:1},f=i.prototype.emit;i(o.prototype),o.prototype.subEvents=function(){if(!this.subs){var t=this.io;this.subs=[s(t,"open",c(this,"onopen")),s(t,"packet",c(this,"onpacket")),s(t,"close",c(this,"onclose"))]}},o.prototype.open=o.prototype.connect=function(){return this.connected?this:(this.subEvents(),this.io.open(),"open"==this.io.readyState&&this.onopen(),this)},o.prototype.send=function(){var t=a(arguments);return t.unshift("message"),this.emit.apply(this,t),this},o.prototype.emit=function(t){if(p.hasOwnProperty(t))return f.apply(this,arguments),this;var e=a(arguments),n=r.EVENT;u(e)&&(n=r.BINARY_EVENT);var o={type:n,data:e};return"function"==typeof e[e.length-1]&&(l("emitting packet with ack id %d",this.ids),this.acks[this.ids]=e.pop(),o.id=this.ids++),this.connected?this.packet(o):this.sendBuffer.push(o),this},o.prototype.packet=function(t){t.nsp=this.nsp,this.io.packet(t)},o.prototype.onopen=function(){l("transport is open - connecting"),"/"!=this.nsp&&this.packet({type:r.CONNECT})},o.prototype.onclose=function(t){l("close (%s)",t),this.connected=!1,this.disconnected=!0,delete this.id,this.emit("disconnect",t)},o.prototype.onpacket=function(t){if(t.nsp==this.nsp)switch(t.type){case r.CONNECT:this.onconnect();break;case r.EVENT:this.onevent(t);break;case r.BINARY_EVENT:this.onevent(t);break;case r.ACK:this.onack(t);break;case r.BINARY_ACK:this.onack(t);break;case r.DISCONNECT:this.ondisconnect();break;case r.ERROR:this.emit("error",t.data)}},o.prototype.onevent=function(t){var e=t.data||[];l("emitting event %j",e),null!=t.id&&(l("attaching ack callback to event"),e.push(this.ack(t.id))),this.connected?f.apply(this,e):this.receiveBuffer.push(e)},o.prototype.ack=function(t){var e=this,n=!1;return function(){if(!n){n=!0;var o=a(arguments);l("sending ack %j",o);var i=u(o)?r.BINARY_ACK:r.ACK;e.packet({type:i,id:t,data:o})}}},o.prototype.onack=function(t){l("calling ack %s with %j",t.id,t.data);var e=this.acks[t.id];e.apply(this,t.data),delete this.acks[t.id]},o.prototype.onconnect=function(){this.connected=!0,this.disconnected=!1,this.emit("connect"),this.emitBuffered()},o.prototype.emitBuffered=function(){var t;for(t=0;t<this.receiveBuffer.length;t++)f.apply(this,this.receiveBuffer[t]);for(this.receiveBuffer=[],t=0;t<this.sendBuffer.length;t++)this.packet(this.sendBuffer[t]);this.sendBuffer=[]},o.prototype.ondisconnect=function(){l("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")},o.prototype.destroy=function(){if(this.subs){for(var t=0;t<this.subs.length;t++)this.subs[t].destroy();this.subs=null}this.io.destroy(this)},o.prototype.close=o.prototype.disconnect=function(){return this.connected&&(l("performing disconnect (%s)",this.nsp),this.packet({type:r.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}},{"./on":4,"component-bind":8,"component-emitter":9,debug:10,"has-binary":36,"socket.io-parser":44,"to-array":48}],6:[function(t,e){(function(n){function o(t,e){var o=t,e=e||n.location;return null==t&&(t=e.protocol+"//"+e.host),"string"==typeof t&&("/"==t.charAt(0)&&(t="/"==t.charAt(1)?e.protocol+t:e.hostname+t),/^(https?|wss?):\/\//.test(t)||(i("protocol-less url %s",t),t="undefined"!=typeof e?e.protocol+"//"+t:"https://"+t),i("parse %s",t),o=r(t)),o.port||(/^(http|ws)$/.test(o.protocol)?o.port="80":/^(http|ws)s$/.test(o.protocol)&&(o.port="443")),o.path=o.path||"/",o.id=o.protocol+"://"+o.host+":"+o.port,o.href=o.protocol+"://"+o.host+(e&&e.port==o.port?"":":"+o.port),o}var r=t("parseuri"),i=t("debug")("socket.io-client:url");e.exports=o}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{});
},{debug:10,parseuri:42}],7:[function(t,e){function n(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=t.jitter>0&&t.jitter<=1?t.jitter:0,this.attempts=0}e.exports=n,n.prototype.duration=function(){var t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var e=Math.random(),n=Math.floor(e*this.jitter*t);t=0==(1&Math.floor(10*e))?t-n:t+n}return 0|Math.min(t,this.max)},n.prototype.reset=function(){this.attempts=0},n.prototype.setMin=function(t){this.ms=t},n.prototype.setMax=function(t){this.max=t},n.prototype.setJitter=function(t){this.jitter=t}},{}],8:[function(t,e){var n=[].slice;e.exports=function(t,e){if("string"==typeof e&&(e=t[e]),"function"!=typeof e)throw new Error("bind() requires a function");var o=n.call(arguments,2);return function(){return e.apply(t,o.concat(n.call(arguments)))}}},{}],9:[function(t,e){function n(t){return t?o(t):void 0}function o(t){for(var e in n.prototype)t[e]=n.prototype[e];return t}e.exports=n,n.prototype.on=n.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks[t]=this._callbacks[t]||[]).push(e),this},n.prototype.once=function(t,e){function n(){o.off(t,n),e.apply(this,arguments)}var o=this;return this._callbacks=this._callbacks||{},n.fn=e,this.on(t,n),this},n.prototype.off=n.prototype.removeListener=n.prototype.removeAllListeners=n.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n=this._callbacks[t];if(!n)return this;if(1==arguments.length)return delete this._callbacks[t],this;for(var o,r=0;r<n.length;r++)if(o=n[r],o===e||o.fn===e){n.splice(r,1);break}return this},n.prototype.emit=function(t){this._callbacks=this._callbacks||{};var e=[].slice.call(arguments,1),n=this._callbacks[t];if(n){n=n.slice(0);for(var o=0,r=n.length;r>o;++o)n[o].apply(this,e)}return this},n.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks[t]||[]},n.prototype.hasListeners=function(t){return!!this.listeners(t).length}},{}],10:[function(t,e){function n(t){return n.enabled(t)?function(e){e=o(e);var r=new Date,i=r-(n[t]||r);n[t]=r,e=t+" "+e+" +"+n.humanize(i),window.console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)}:function(){}}function o(t){return t instanceof Error?t.stack||t.message:t}e.exports=n,n.names=[],n.skips=[],n.enable=function(t){try{localStorage.debug=t}catch(e){}for(var o=(t||"").split(/[\s,]+/),r=o.length,i=0;r>i;i++)t=o[i].replace("*",".*?"),"-"===t[0]?n.skips.push(new RegExp("^"+t.substr(1)+"$")):n.names.push(new RegExp("^"+t+"$"))},n.disable=function(){n.enable("")},n.humanize=function(t){var e=1e3,n=6e4,o=60*n;return t>=o?(t/o).toFixed(1)+"h":t>=n?(t/n).toFixed(1)+"m":t>=e?(t/e|0)+"s":t+"ms"},n.enabled=function(t){for(var e=0,o=n.skips.length;o>e;e++)if(n.skips[e].test(t))return!1;for(var e=0,o=n.names.length;o>e;e++)if(n.names[e].test(t))return!0;return!1};try{window.localStorage&&n.enable(localStorage.debug)}catch(r){}},{}],11:[function(t,e){e.exports=t("./lib/")},{"./lib/":12}],12:[function(t,e){e.exports=t("./socket"),e.exports.parser=t("engine.io-parser")},{"./socket":13,"engine.io-parser":25}],13:[function(t,e){(function(n){function o(t,e){if(!(this instanceof o))return new o(t,e);if(e=e||{},t&&"object"==typeof t&&(e=t,t=null),t&&(t=u(t),e.host=t.host,e.secure="https"==t.protocol||"wss"==t.protocol,e.port=t.port,t.query&&(e.query=t.query)),this.secure=null!=e.secure?e.secure:n.location&&"https:"==location.protocol,e.host){var r=e.host.split(":");e.hostname=r.shift(),r.length?e.port=r.pop():e.port||(e.port=this.secure?"443":"80")}this.agent=e.agent||!1,this.hostname=e.hostname||(n.location?location.hostname:"localhost"),this.port=e.port||(n.location&&location.port?location.port:this.secure?443:80),this.query=e.query||{},"string"==typeof this.query&&(this.query=f.decode(this.query)),this.upgrade=!1!==e.upgrade,this.path=(e.path||"/engine.io").replace(/\/$/,"")+"/",this.forceJSONP=!!e.forceJSONP,this.jsonp=!1!==e.jsonp,this.forceBase64=!!e.forceBase64,this.enablesXDR=!!e.enablesXDR,this.timestampParam=e.timestampParam||"t",this.timestampRequests=e.timestampRequests,this.transports=e.transports||["polling","websocket"],this.readyState="",this.writeBuffer=[],this.callbackBuffer=[],this.policyPort=e.policyPort||843,this.rememberUpgrade=e.rememberUpgrade||!1,this.binaryType=null,this.onlyBinaryUpgrades=e.onlyBinaryUpgrades,this.pfx=e.pfx||null,this.key=e.key||null,this.passphrase=e.passphrase||null,this.cert=e.cert||null,this.ca=e.ca||null,this.ciphers=e.ciphers||null,this.rejectUnauthorized=e.rejectUnauthorized||null,this.open()}function r(t){var e={};for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e}var i=t("./transports"),a=t("component-emitter"),s=t("debug")("engine.io-client:socket"),c=t("indexof"),l=t("engine.io-parser"),u=t("parseuri"),p=t("parsejson"),f=t("parseqs");e.exports=o,o.priorWebsocketSuccess=!1,a(o.prototype),o.protocol=l.protocol,o.Socket=o,o.Transport=t("./transport"),o.transports=t("./transports"),o.parser=t("engine.io-parser"),o.prototype.createTransport=function(t){s('creating transport "%s"',t);var e=r(this.query);e.EIO=l.protocol,e.transport=t,this.id&&(e.sid=this.id);var n=new i[t]({agent:this.agent,hostname:this.hostname,port:this.port,secure:this.secure,path:this.path,query:e,forceJSONP:this.forceJSONP,jsonp:this.jsonp,forceBase64:this.forceBase64,enablesXDR:this.enablesXDR,timestampRequests:this.timestampRequests,timestampParam:this.timestampParam,policyPort:this.policyPort,socket:this,pfx:this.pfx,key:this.key,passphrase:this.passphrase,cert:this.cert,ca:this.ca,ciphers:this.ciphers,rejectUnauthorized:this.rejectUnauthorized});return n},o.prototype.open=function(){var t;if(this.rememberUpgrade&&o.priorWebsocketSuccess&&-1!=this.transports.indexOf("websocket"))t="websocket";else{if(0==this.transports.length){var e=this;return void setTimeout(function(){e.emit("error","No transports available")},0)}t=this.transports[0]}this.readyState="opening";var t;try{t=this.createTransport(t)}catch(n){return this.transports.shift(),void this.open()}t.open(),this.setTransport(t)},o.prototype.setTransport=function(t){s("setting transport %s",t.name);var e=this;this.transport&&(s("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),this.transport=t,t.on("drain",function(){e.onDrain()}).on("packet",function(t){e.onPacket(t)}).on("error",function(t){e.onError(t)}).on("close",function(){e.onClose("transport close")})},o.prototype.probe=function(t){function e(){if(f.onlyBinaryUpgrades){var e=!this.supportsBinary&&f.transport.supportsBinary;p=p||e}p||(s('probe transport "%s" opened',t),u.send([{type:"ping",data:"probe"}]),u.once("packet",function(e){if(!p)if("pong"==e.type&&"probe"==e.data){if(s('probe transport "%s" pong',t),f.upgrading=!0,f.emit("upgrading",u),!u)return;o.priorWebsocketSuccess="websocket"==u.name,s('pausing current transport "%s"',f.transport.name),f.transport.pause(function(){p||"closed"!=f.readyState&&(s("changing transport and sending upgrade packet"),l(),f.setTransport(u),u.send([{type:"upgrade"}]),f.emit("upgrade",u),u=null,f.upgrading=!1,f.flush())})}else{s('probe transport "%s" failed',t);var n=new Error("probe error");n.transport=u.name,f.emit("upgradeError",n)}}))}function n(){p||(p=!0,l(),u.close(),u=null)}function r(e){var o=new Error("probe error: "+e);o.transport=u.name,n(),s('probe transport "%s" failed because of error: %s',t,e),f.emit("upgradeError",o)}function i(){r("transport closed")}function a(){r("socket closed")}function c(t){u&&t.name!=u.name&&(s('"%s" works - aborting "%s"',t.name,u.name),n())}function l(){u.removeListener("open",e),u.removeListener("error",r),u.removeListener("close",i),f.removeListener("close",a),f.removeListener("upgrading",c)}s('probing transport "%s"',t);var u=this.createTransport(t,{probe:1}),p=!1,f=this;o.priorWebsocketSuccess=!1,u.once("open",e),u.once("error",r),u.once("close",i),this.once("close",a),this.once("upgrading",c),u.open()},o.prototype.onOpen=function(){if(s("socket open"),this.readyState="open",o.priorWebsocketSuccess="websocket"==this.transport.name,this.emit("open"),this.flush(),"open"==this.readyState&&this.upgrade&&this.transport.pause){s("starting upgrade probes");for(var t=0,e=this.upgrades.length;e>t;t++)this.probe(this.upgrades[t])}},o.prototype.onPacket=function(t){if("opening"==this.readyState||"open"==this.readyState)switch(s('socket receive: type "%s", data "%s"',t.type,t.data),this.emit("packet",t),this.emit("heartbeat"),t.type){case"open":this.onHandshake(p(t.data));break;case"pong":this.setPing();break;case"error":var e=new Error("server error");e.code=t.data,this.emit("error",e);break;case"message":this.emit("data",t.data),this.emit("message",t.data)}else s('packet received with socket readyState "%s"',this.readyState)},o.prototype.onHandshake=function(t){this.emit("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this.upgrades=this.filterUpgrades(t.upgrades),this.pingInterval=t.pingInterval,this.pingTimeout=t.pingTimeout,this.onOpen(),"closed"!=this.readyState&&(this.setPing(),this.removeListener("heartbeat",this.onHeartbeat),this.on("heartbeat",this.onHeartbeat))},o.prototype.onHeartbeat=function(t){clearTimeout(this.pingTimeoutTimer);var e=this;e.pingTimeoutTimer=setTimeout(function(){"closed"!=e.readyState&&e.onClose("ping timeout")},t||e.pingInterval+e.pingTimeout)},o.prototype.setPing=function(){var t=this;clearTimeout(t.pingIntervalTimer),t.pingIntervalTimer=setTimeout(function(){s("writing ping packet - expecting pong within %sms",t.pingTimeout),t.ping(),t.onHeartbeat(t.pingTimeout)},t.pingInterval)},o.prototype.ping=function(){this.sendPacket("ping")},o.prototype.onDrain=function(){for(var t=0;t<this.prevBufferLen;t++)this.callbackBuffer[t]&&this.callbackBuffer[t]();this.writeBuffer.splice(0,this.prevBufferLen),this.callbackBuffer.splice(0,this.prevBufferLen),this.prevBufferLen=0,0==this.writeBuffer.length?this.emit("drain"):this.flush()},o.prototype.flush=function(){"closed"!=this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length&&(s("flushing %d packets in socket",this.writeBuffer.length),this.transport.send(this.writeBuffer),this.prevBufferLen=this.writeBuffer.length,this.emit("flush"))},o.prototype.write=o.prototype.send=function(t,e){return this.sendPacket("message",t,e),this},o.prototype.sendPacket=function(t,e,n){if("closing"!=this.readyState&&"closed"!=this.readyState){var o={type:t,data:e};this.emit("packetCreate",o),this.writeBuffer.push(o),this.callbackBuffer.push(n),this.flush()}},o.prototype.close=function(){function t(){o.onClose("forced close"),s("socket closing - telling transport to close"),o.transport.close()}function e(){o.removeListener("upgrade",e),o.removeListener("upgradeError",e),t()}function n(){o.once("upgrade",e),o.once("upgradeError",e)}if("opening"==this.readyState||"open"==this.readyState){this.readyState="closing";var o=this;this.writeBuffer.length?this.once("drain",function(){this.upgrading?n():t()}):this.upgrading?n():t()}return this},o.prototype.onError=function(t){s("socket error %j",t),o.priorWebsocketSuccess=!1,this.emit("error",t),this.onClose("transport error",t)},o.prototype.onClose=function(t,e){if("opening"==this.readyState||"open"==this.readyState||"closing"==this.readyState){s('socket close with reason: "%s"',t);var n=this;clearTimeout(this.pingIntervalTimer),clearTimeout(this.pingTimeoutTimer),setTimeout(function(){n.writeBuffer=[],n.callbackBuffer=[],n.prevBufferLen=0},0),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),this.readyState="closed",this.id=null,this.emit("close",t,e)}},o.prototype.filterUpgrades=function(t){for(var e=[],n=0,o=t.length;o>n;n++)~c(this.transports,t[n])&&e.push(t[n]);return e}}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./transport":14,"./transports":15,"component-emitter":9,debug:22,"engine.io-parser":25,indexof:40,parsejson:32,parseqs:33,parseuri:34}],14:[function(t,e){function n(t){this.path=t.path,this.hostname=t.hostname,this.port=t.port,this.secure=t.secure,this.query=t.query,this.timestampParam=t.timestampParam,this.timestampRequests=t.timestampRequests,this.readyState="",this.agent=t.agent||!1,this.socket=t.socket,this.enablesXDR=t.enablesXDR,this.pfx=t.pfx,this.key=t.key,this.passphrase=t.passphrase,this.cert=t.cert,this.ca=t.ca,this.ciphers=t.ciphers,this.rejectUnauthorized=t.rejectUnauthorized}var o=t("engine.io-parser"),r=t("component-emitter");e.exports=n,r(n.prototype),n.timestamps=0,n.prototype.onError=function(t,e){var n=new Error(t);return n.type="TransportError",n.description=e,this.emit("error",n),this},n.prototype.open=function(){return("closed"==this.readyState||""==this.readyState)&&(this.readyState="opening",this.doOpen()),this},n.prototype.close=function(){return("opening"==this.readyState||"open"==this.readyState)&&(this.doClose(),this.onClose()),this},n.prototype.send=function(t){if("open"!=this.readyState)throw new Error("Transport not open");this.write(t)},n.prototype.onOpen=function(){this.readyState="open",this.writable=!0,this.emit("open")},n.prototype.onData=function(t){var e=o.decodePacket(t,this.socket.binaryType);this.onPacket(e)},n.prototype.onPacket=function(t){this.emit("packet",t)},n.prototype.onClose=function(){this.readyState="closed",this.emit("close")}},{"component-emitter":9,"engine.io-parser":25}],15:[function(t,e,n){(function(e){function o(t){var n,o=!1,s=!1,c=!1!==t.jsonp;if(e.location){var l="https:"==location.protocol,u=location.port;u||(u=l?443:80),o=t.hostname!=location.hostname||u!=t.port,s=t.secure!=l}if(t.xdomain=o,t.xscheme=s,n=new r(t),"open"in n&&!t.forceJSONP)return new i(t);if(!c)throw new Error("JSONP disabled");return new a(t)}var r=t("xmlhttprequest"),i=t("./polling-xhr"),a=t("./polling-jsonp"),s=t("./websocket");n.polling=o,n.websocket=s}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./polling-jsonp":16,"./polling-xhr":17,"./websocket":19,xmlhttprequest:20}],16:[function(t,e){(function(n){function o(){}function r(t){i.call(this,t),this.query=this.query||{},s||(n.___eio||(n.___eio=[]),s=n.___eio),this.index=s.length;var e=this;s.push(function(t){e.onData(t)}),this.query.j=this.index,n.document&&n.addEventListener&&n.addEventListener("beforeunload",function(){e.script&&(e.script.onerror=o)},!1)}var i=t("./polling"),a=t("component-inherit");e.exports=r;var s,c=/\n/g,l=/\\n/g;a(r,i),r.prototype.supportsBinary=!1,r.prototype.doClose=function(){this.script&&(this.script.parentNode.removeChild(this.script),this.script=null),this.form&&(this.form.parentNode.removeChild(this.form),this.form=null,this.iframe=null),i.prototype.doClose.call(this)},r.prototype.doPoll=function(){var t=this,e=document.createElement("script");this.script&&(this.script.parentNode.removeChild(this.script),this.script=null),e.async=!0,e.src=this.uri(),e.onerror=function(e){t.onError("jsonp poll error",e)};var n=document.getElementsByTagName("script")[0];n.parentNode.insertBefore(e,n),this.script=e;var o="undefined"!=typeof navigator&&/gecko/i.test(navigator.userAgent);o&&setTimeout(function(){var t=document.createElement("iframe");document.body.appendChild(t),document.body.removeChild(t)},100)},r.prototype.doWrite=function(t,e){function n(){o(),e()}function o(){if(r.iframe)try{r.form.removeChild(r.iframe)}catch(t){r.onError("jsonp polling iframe removal error",t)}try{var e='<iframe src="javascript:0" name="'+r.iframeId+'">';i=document.createElement(e)}catch(t){i=document.createElement("iframe"),i.name=r.iframeId,i.src="javascript:0"}i.id=r.iframeId,r.form.appendChild(i),r.iframe=i}var r=this;if(!this.form){var i,a=document.createElement("form"),s=document.createElement("textarea"),u=this.iframeId="eio_iframe_"+this.index;a.className="socketio",a.style.position="absolute",a.style.top="-1000px",a.style.left="-1000px",a.target=u,a.method="POST",a.setAttribute("accept-charset","utf-8"),s.name="d",a.appendChild(s),document.body.appendChild(a),this.form=a,this.area=s}this.form.action=this.uri(),o(),t=t.replace(l,"\\\n"),this.area.value=t.replace(c,"\\n");try{this.form.submit()}catch(p){}this.iframe.attachEvent?this.iframe.onreadystatechange=function(){"complete"==r.iframe.readyState&&n()}:this.iframe.onload=n}}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./polling":18,"component-inherit":21}],17:[function(t,e){(function(n){function o(){}function r(t){if(c.call(this,t),n.location){var e="https:"==location.protocol,o=location.port;o||(o=e?443:80),this.xd=t.hostname!=n.location.hostname||o!=t.port,this.xs=t.secure!=e}}function i(t){this.method=t.method||"GET",this.uri=t.uri,this.xd=!!t.xd,this.xs=!!t.xs,this.async=!1!==t.async,this.data=void 0!=t.data?t.data:null,this.agent=t.agent,this.isBinary=t.isBinary,this.supportsBinary=t.supportsBinary,this.enablesXDR=t.enablesXDR,this.pfx=t.pfx,this.key=t.key,this.passphrase=t.passphrase,this.cert=t.cert,this.ca=t.ca,this.ciphers=t.ciphers,this.rejectUnauthorized=t.rejectUnauthorized,this.create()}function a(){for(var t in i.requests)i.requests.hasOwnProperty(t)&&i.requests[t].abort()}var s=t("xmlhttprequest"),c=t("./polling"),l=t("component-emitter"),u=t("component-inherit"),p=t("debug")("engine.io-client:polling-xhr");e.exports=r,e.exports.Request=i,u(r,c),r.prototype.supportsBinary=!0,r.prototype.request=function(t){return t=t||{},t.uri=this.uri(),t.xd=this.xd,t.xs=this.xs,t.agent=this.agent||!1,t.supportsBinary=this.supportsBinary,t.enablesXDR=this.enablesXDR,t.pfx=this.pfx,t.key=this.key,t.passphrase=this.passphrase,t.cert=this.cert,t.ca=this.ca,t.ciphers=this.ciphers,t.rejectUnauthorized=this.rejectUnauthorized,new i(t)},r.prototype.doWrite=function(t,e){var n="string"!=typeof t&&void 0!==t,o=this.request({method:"POST",data:t,isBinary:n}),r=this;o.on("success",e),o.on("error",function(t){r.onError("xhr post error",t)}),this.sendXhr=o},r.prototype.doPoll=function(){p("xhr poll");var t=this.request(),e=this;t.on("data",function(t){e.onData(t)}),t.on("error",function(t){e.onError("xhr poll error",t)}),this.pollXhr=t},l(i.prototype),i.prototype.create=function(){var t={agent:this.agent,xdomain:this.xd,xscheme:this.xs,enablesXDR:this.enablesXDR};t.pfx=this.pfx,t.key=this.key,t.passphrase=this.passphrase,t.cert=this.cert,t.ca=this.ca,t.ciphers=this.ciphers,t.rejectUnauthorized=this.rejectUnauthorized;var e=this.xhr=new s(t),o=this;try{if(p("xhr open %s: %s",this.method,this.uri),e.open(this.method,this.uri,this.async),this.supportsBinary&&(e.responseType="arraybuffer"),"POST"==this.method)try{this.isBinary?e.setRequestHeader("Content-type","application/octet-stream"):e.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(r){}"withCredentials"in e&&(e.withCredentials=!0),this.hasXDR()?(e.onload=function(){o.onLoad()},e.onerror=function(){o.onError(e.responseText)}):e.onreadystatechange=function(){4==e.readyState&&(200==e.status||1223==e.status?o.onLoad():setTimeout(function(){o.onError(e.status)},0))},p("xhr data %s",this.data),e.send(this.data)}catch(r){return void setTimeout(function(){o.onError(r)},0)}n.document&&(this.index=i.requestsCount++,i.requests[this.index]=this)},i.prototype.onSuccess=function(){this.emit("success"),this.cleanup()},i.prototype.onData=function(t){this.emit("data",t),this.onSuccess()},i.prototype.onError=function(t){this.emit("error",t),this.cleanup(!0)},i.prototype.cleanup=function(t){if("undefined"!=typeof this.xhr&&null!==this.xhr){if(this.hasXDR()?this.xhr.onload=this.xhr.onerror=o:this.xhr.onreadystatechange=o,t)try{this.xhr.abort()}catch(e){}n.document&&delete i.requests[this.index],this.xhr=null}},i.prototype.onLoad=function(){var t;try{var e;try{e=this.xhr.getResponseHeader("Content-Type").split(";")[0]}catch(n){}t="application/octet-stream"===e?this.xhr.response:this.supportsBinary?"ok":this.xhr.responseText}catch(n){this.onError(n)}null!=t&&this.onData(t)},i.prototype.hasXDR=function(){return"undefined"!=typeof n.XDomainRequest&&!this.xs&&this.enablesXDR},i.prototype.abort=function(){this.cleanup()},n.document&&(i.requestsCount=0,i.requests={},n.attachEvent?n.attachEvent("onunload",a):n.addEventListener&&n.addEventListener("beforeunload",a,!1))}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./polling":18,"component-emitter":9,"component-inherit":21,debug:22,xmlhttprequest:20}],18:[function(t,e){function n(t){var e=t&&t.forceBase64;(!c||e)&&(this.supportsBinary=!1),o.call(this,t)}var o=t("../transport"),r=t("parseqs"),i=t("engine.io-parser"),a=t("component-inherit"),s=t("debug")("engine.io-client:polling");e.exports=n;var c=function(){var e=t("xmlhttprequest"),n=new e({xdomain:!1});return null!=n.responseType}();a(n,o),n.prototype.name="polling",n.prototype.doOpen=function(){this.poll()},n.prototype.pause=function(t){function e(){s("paused"),n.readyState="paused",t()}var n=this;if(this.readyState="pausing",this.polling||!this.writable){var o=0;this.polling&&(s("we are currently polling - waiting to pause"),o++,this.once("pollComplete",function(){s("pre-pause polling complete"),--o||e()})),this.writable||(s("we are currently writing - waiting to pause"),o++,this.once("drain",function(){s("pre-pause writing complete"),--o||e()}))}else e()},n.prototype.poll=function(){s("polling"),this.polling=!0,this.doPoll(),this.emit("poll")},n.prototype.onData=function(t){var e=this;s("polling got data %s",t);var n=function(t){return"opening"==e.readyState&&e.onOpen(),"close"==t.type?(e.onClose(),!1):void e.onPacket(t)};i.decodePayload(t,this.socket.binaryType,n),"closed"!=this.readyState&&(this.polling=!1,this.emit("pollComplete"),"open"==this.readyState?this.poll():s('ignoring poll - transport state "%s"',this.readyState))},n.prototype.doClose=function(){function t(){s("writing close packet"),e.write([{type:"close"}])}var e=this;"open"==this.readyState?(s("transport open - closing"),t()):(s("transport not open - deferring close"),this.once("open",t))},n.prototype.write=function(t){var e=this;this.writable=!1;var n=function(){e.writable=!0,e.emit("drain")},e=this;i.encodePayload(t,this.supportsBinary,function(t){e.doWrite(t,n)})},n.prototype.uri=function(){var t=this.query||{},e=this.secure?"https":"http",n="";return!1!==this.timestampRequests&&(t[this.timestampParam]=+new Date+"-"+o.timestamps++),this.supportsBinary||t.sid||(t.b64=1),t=r.encode(t),this.port&&("https"==e&&443!=this.port||"http"==e&&80!=this.port)&&(n=":"+this.port),t.length&&(t="?"+t),e+"://"+this.hostname+n+this.path+t}},{"../transport":14,"component-inherit":21,debug:22,"engine.io-parser":25,parseqs:33,xmlhttprequest:20}],19:[function(t,e){function n(t){var e=t&&t.forceBase64;e&&(this.supportsBinary=!1),o.call(this,t)}var o=t("../transport"),r=t("engine.io-parser"),i=t("parseqs"),a=t("component-inherit"),s=t("debug")("engine.io-client:websocket"),c=t("ws");e.exports=n,a(n,o),n.prototype.name="websocket",n.prototype.supportsBinary=!0,n.prototype.doOpen=function(){if(this.check()){var t=this.uri(),e=void 0,n={agent:this.agent};n.pfx=this.pfx,n.key=this.key,n.passphrase=this.passphrase,n.cert=this.cert,n.ca=this.ca,n.ciphers=this.ciphers,n.rejectUnauthorized=this.rejectUnauthorized,this.ws=new c(t,e,n),void 0===this.ws.binaryType&&(this.supportsBinary=!1),this.ws.binaryType="arraybuffer",this.addEventListeners()}},n.prototype.addEventListeners=function(){var t=this;this.ws.onopen=function(){t.onOpen()},this.ws.onclose=function(){t.onClose()},this.ws.onmessage=function(e){t.onData(e.data)},this.ws.onerror=function(e){t.onError("websocket error",e)}},"undefined"!=typeof navigator&&/iPad|iPhone|iPod/i.test(navigator.userAgent)&&(n.prototype.onData=function(t){var e=this;setTimeout(function(){o.prototype.onData.call(e,t)},0)}),n.prototype.write=function(t){function e(){n.writable=!0,n.emit("drain")}var n=this;this.writable=!1;for(var o=0,i=t.length;i>o;o++)r.encodePacket(t[o],this.supportsBinary,function(t){try{n.ws.send(t)}catch(e){s("websocket closed before onclose event")}});setTimeout(e,0)},n.prototype.onClose=function(){o.prototype.onClose.call(this)},n.prototype.doClose=function(){"undefined"!=typeof this.ws&&this.ws.close()},n.prototype.uri=function(){var t=this.query||{},e=this.secure?"wss":"ws",n="";return this.port&&("wss"==e&&443!=this.port||"ws"==e&&80!=this.port)&&(n=":"+this.port),this.timestampRequests&&(t[this.timestampParam]=+new Date),this.supportsBinary||(t.b64=1),t=i.encode(t),t.length&&(t="?"+t),e+"://"+this.hostname+n+this.path+t},n.prototype.check=function(){return!(!c||"__initialize"in c&&this.name===n.prototype.name)}},{"../transport":14,"component-inherit":21,debug:22,"engine.io-parser":25,parseqs:33,ws:35}],20:[function(t,e){var n=t("has-cors");e.exports=function(t){var e=t.xdomain,o=t.xscheme,r=t.enablesXDR;try{if("undefined"!=typeof XMLHttpRequest&&(!e||n))return new XMLHttpRequest}catch(i){}try{if("undefined"!=typeof XDomainRequest&&!o&&r)return new XDomainRequest}catch(i){}if(!e)try{return new ActiveXObject("Microsoft.XMLHTTP")}catch(i){}}},{"has-cors":38}],21:[function(t,e){e.exports=function(t,e){var n=function(){};n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}},{}],22:[function(t,e,n){function o(){return"WebkitAppearance"in document.documentElement.style||window.console&&(console.firebug||console.exception&&console.table)||navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31}function r(){var t=arguments,e=this.useColors;if(t[0]=(e?"%c":"")+this.namespace+(e?" %c":" ")+t[0]+(e?"%c ":" ")+"+"+n.humanize(this.diff),!e)return t;var o="color: "+this.color;t=[t[0],o,"color: inherit"].concat(Array.prototype.slice.call(t,1));var r=0,i=0;return t[0].replace(/%[a-z%]/g,function(t){"%%"!==t&&(r++,"%c"===t&&(i=r))}),t.splice(i,0,o),t}function i(){return"object"==typeof console&&"function"==typeof console.log&&Function.prototype.apply.call(console.log,console,arguments)}function a(t){try{null==t?localStorage.removeItem("debug"):localStorage.debug=t}catch(e){}}function s(){var t;try{t=localStorage.debug}catch(e){}return t}n=e.exports=t("./debug"),n.log=i,n.formatArgs=r,n.save=a,n.load=s,n.useColors=o,n.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"],n.formatters.j=function(t){return JSON.stringify(t)},n.enable(s())},{"./debug":23}],23:[function(t,e,n){function o(){return n.colors[u++%n.colors.length]}function r(t){function e(){}function r(){var t=r,e=+new Date,i=e-(l||e);t.diff=i,t.prev=l,t.curr=e,l=e,null==t.useColors&&(t.useColors=n.useColors()),null==t.color&&t.useColors&&(t.color=o());var a=Array.prototype.slice.call(arguments);a[0]=n.coerce(a[0]),"string"!=typeof a[0]&&(a=["%o"].concat(a));var s=0;a[0]=a[0].replace(/%([a-z%])/g,function(e,o){if("%%"===e)return e;s++;var r=n.formatters[o];if("function"==typeof r){var i=a[s];e=r.call(t,i),a.splice(s,1),s--}return e}),"function"==typeof n.formatArgs&&(a=n.formatArgs.apply(t,a));var c=r.log||n.log||void 0;c.apply(t,a)}e.enabled=!1,r.enabled=!0;var i=n.enabled(t)?r:e;return i.namespace=t,i}function i(t){n.save(t);for(var e=(t||"").split(/[\s,]+/),o=e.length,r=0;o>r;r++)e[r]&&(t=e[r].replace(/\*/g,".*?"),"-"===t[0]?n.skips.push(new RegExp("^"+t.substr(1)+"$")):n.names.push(new RegExp("^"+t+"$")))}function a(){n.enable("")}function s(t){var e,o;for(e=0,o=n.skips.length;o>e;e++)if(n.skips[e].test(t))return!1;for(e=0,o=n.names.length;o>e;e++)if(n.names[e].test(t))return!0;return!1}function c(t){return t instanceof Error?t.stack||t.message:t}n=e.exports=r,n.coerce=c,n.disable=a,n.enable=i,n.enabled=s,n.humanize=t("ms"),n.names=[],n.skips=[],n.formatters={};var l,u=0},{ms:24}],24:[function(t,e){function n(t){var e=/^((?:\d+)?\.?\d+) *(ms|seconds?|s|minutes?|m|hours?|h|days?|d|years?|y)?$/i.exec(t);if(e){var n=parseFloat(e[1]),o=(e[2]||"ms").toLowerCase();switch(o){case"years":case"year":case"y":return n*u;case"days":case"day":case"d":return n*l;case"hours":case"hour":case"h":return n*c;case"minutes":case"minute":case"m":return n*s;case"seconds":case"second":case"s":return n*a;case"ms":return n}}}function o(t){return t>=l?Math.round(t/l)+"d":t>=c?Math.round(t/c)+"h":t>=s?Math.round(t/s)+"m":t>=a?Math.round(t/a)+"s":t+"ms"}function r(t){return i(t,l,"day")||i(t,c,"hour")||i(t,s,"minute")||i(t,a,"second")||t+" ms"}function i(t,e,n){return e>t?void 0:1.5*e>t?Math.floor(t/e)+" "+n:Math.ceil(t/e)+" "+n+"s"}var a=1e3,s=60*a,c=60*s,l=24*c,u=365.25*l;e.exports=function(t,e){return e=e||{},"string"==typeof t?n(t):e["long"]?r(t):o(t)}},{}],25:[function(t,e,n){(function(e){function o(t,e){var o="b"+n.packets[t.type]+t.data.data;return e(o)}function r(t,e,o){if(!e)return n.encodeBase64Packet(t,o);var r=t.data,i=new Uint8Array(r),a=new Uint8Array(1+r.byteLength);a[0]=y[t.type];for(var s=0;s<i.length;s++)a[s+1]=i[s];return o(a.buffer)}function i(t,e,o){if(!e)return n.encodeBase64Packet(t,o);var r=new FileReader;return r.onload=function(){t.data=r.result,n.encodePacket(t,e,!0,o)},r.readAsArrayBuffer(t.data)}function a(t,e,o){if(!e)return n.encodeBase64Packet(t,o);if(g)return i(t,e,o);var r=new Uint8Array(1);r[0]=y[t.type];var a=new _([r.buffer,t.data]);return o(a)}function s(t,e,n){for(var o=new Array(t.length),r=f(t.length,n),i=function(t,n,r){e(n,function(e,n){o[t]=n,r(e,o)})},a=0;a<t.length;a++)i(a,t[a],r)}var c=t("./keys"),l=t("has-binary"),u=t("arraybuffer.slice"),p=t("base64-arraybuffer"),f=t("after"),h=t("utf8"),d=navigator.userAgent.match(/Android/i),m=/PhantomJS/i.test(navigator.userAgent),g=d||m;n.protocol=3;var y=n.packets={open:0,close:1,ping:2,pong:3,message:4,upgrade:5,noop:6},v=c(y),b={type:"error",data:"parser error"},_=t("blob");n.encodePacket=function(t,n,i,s){"function"==typeof n&&(s=n,n=!1),"function"==typeof i&&(s=i,i=null);var c=void 0===t.data?void 0:t.data.buffer||t.data;if(e.ArrayBuffer&&c instanceof ArrayBuffer)return r(t,n,s);if(_&&c instanceof e.Blob)return a(t,n,s);if(c&&c.base64)return o(t,s);var l=y[t.type];return void 0!==t.data&&(l+=i?h.encode(String(t.data)):String(t.data)),s(""+l)},n.encodeBase64Packet=function(t,o){var r="b"+n.packets[t.type];if(_&&t.data instanceof _){var i=new FileReader;return i.onload=function(){var t=i.result.split(",")[1];o(r+t)},i.readAsDataURL(t.data)}var a;try{a=String.fromCharCode.apply(null,new Uint8Array(t.data))}catch(s){for(var c=new Uint8Array(t.data),l=new Array(c.length),u=0;u<c.length;u++)l[u]=c[u];a=String.fromCharCode.apply(null,l)}return r+=e.btoa(a),o(r)},n.decodePacket=function(t,e,o){if("string"==typeof t||void 0===t){if("b"==t.charAt(0))return n.decodeBase64Packet(t.substr(1),e);if(o)try{t=h.decode(t)}catch(r){return b}var i=t.charAt(0);return Number(i)==i&&v[i]?t.length>1?{type:v[i],data:t.substring(1)}:{type:v[i]}:b}var a=new Uint8Array(t),i=a[0],s=u(t,1);return _&&"blob"===e&&(s=new _([s])),{type:v[i],data:s}},n.decodeBase64Packet=function(t,n){var o=v[t.charAt(0)];if(!e.ArrayBuffer)return{type:o,data:{base64:!0,data:t.substr(1)}};var r=p.decode(t.substr(1));return"blob"===n&&_&&(r=new _([r])),{type:o,data:r}},n.encodePayload=function(t,e,o){function r(t){return t.length+":"+t}function i(t,o){n.encodePacket(t,!!a&&e,!0,function(t){o(null,r(t))})}"function"==typeof e&&(o=e,e=null);var a=l(t);return e&&a?_&&!g?n.encodePayloadAsBlob(t,o):n.encodePayloadAsArrayBuffer(t,o):t.length?void s(t,i,function(t,e){return o(e.join(""))}):o("0:")},n.decodePayload=function(t,e,o){if("string"!=typeof t)return n.decodePayloadAsBinary(t,e,o);"function"==typeof e&&(o=e,e=null);var r;if(""==t)return o(b,0,1);for(var i,a,s="",c=0,l=t.length;l>c;c++){var u=t.charAt(c);if(":"!=u)s+=u;else{if(""==s||s!=(i=Number(s)))return o(b,0,1);if(a=t.substr(c+1,i),s!=a.length)return o(b,0,1);if(a.length){if(r=n.decodePacket(a,e,!0),b.type==r.type&&b.data==r.data)return o(b,0,1);var p=o(r,c+i,l);if(!1===p)return}c+=i,s=""}}return""!=s?o(b,0,1):void 0},n.encodePayloadAsArrayBuffer=function(t,e){function o(t,e){
n.encodePacket(t,!0,!0,function(t){return e(null,t)})}return t.length?void s(t,o,function(t,n){var o=n.reduce(function(t,e){var n;return n="string"==typeof e?e.length:e.byteLength,t+n.toString().length+n+2},0),r=new Uint8Array(o),i=0;return n.forEach(function(t){var e="string"==typeof t,n=t;if(e){for(var o=new Uint8Array(t.length),a=0;a<t.length;a++)o[a]=t.charCodeAt(a);n=o.buffer}r[i++]=e?0:1;for(var s=n.byteLength.toString(),a=0;a<s.length;a++)r[i++]=parseInt(s[a]);r[i++]=255;for(var o=new Uint8Array(n),a=0;a<o.length;a++)r[i++]=o[a]}),e(r.buffer)}):e(new ArrayBuffer(0))},n.encodePayloadAsBlob=function(t,e){function o(t,e){n.encodePacket(t,!0,!0,function(t){var n=new Uint8Array(1);if(n[0]=1,"string"==typeof t){for(var o=new Uint8Array(t.length),r=0;r<t.length;r++)o[r]=t.charCodeAt(r);t=o.buffer,n[0]=0}for(var i=t instanceof ArrayBuffer?t.byteLength:t.size,a=i.toString(),s=new Uint8Array(a.length+1),r=0;r<a.length;r++)s[r]=parseInt(a[r]);if(s[a.length]=255,_){var c=new _([n.buffer,s.buffer,t]);e(null,c)}})}s(t,o,function(t,n){return e(new _(n))})},n.decodePayloadAsBinary=function(t,e,o){"function"==typeof e&&(o=e,e=null);for(var r=t,i=[],a=!1;r.byteLength>0;){for(var s=new Uint8Array(r),c=0===s[0],l="",p=1;255!=s[p];p++){if(l.length>310){a=!0;break}l+=s[p]}if(a)return o(b,0,1);r=u(r,2+l.length),l=parseInt(l);var f=u(r,0,l);if(c)try{f=String.fromCharCode.apply(null,new Uint8Array(f))}catch(h){var d=new Uint8Array(f);f="";for(var p=0;p<d.length;p++)f+=String.fromCharCode(d[p])}i.push(f),r=u(r,l)}var m=i.length;i.forEach(function(t,r){o(n.decodePacket(t,e,!0),r,m)})}}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./keys":26,after:27,"arraybuffer.slice":28,"base64-arraybuffer":29,blob:30,"has-binary":36,utf8:31}],26:[function(t,e){e.exports=Object.keys||function(t){var e=[],n=Object.prototype.hasOwnProperty;for(var o in t)n.call(t,o)&&e.push(o);return e}},{}],27:[function(t,e){function n(t,e,n){function r(t,o){if(r.count<=0)throw new Error("after called too many times");--r.count,t?(i=!0,e(t),e=n):0!==r.count||i||e(null,o)}var i=!1;return n=n||o,r.count=t,0===t?e():r}function o(){}e.exports=n},{}],28:[function(t,e){e.exports=function(t,e,n){var o=t.byteLength;if(e=e||0,n=n||o,t.slice)return t.slice(e,n);if(0>e&&(e+=o),0>n&&(n+=o),n>o&&(n=o),e>=o||e>=n||0===o)return new ArrayBuffer(0);for(var r=new Uint8Array(t),i=new Uint8Array(n-e),a=e,s=0;n>a;a++,s++)i[s]=r[a];return i.buffer}},{}],29:[function(t,e,n){!function(t){"use strict";n.encode=function(e){var n,o=new Uint8Array(e),r=o.length,i="";for(n=0;r>n;n+=3)i+=t[o[n]>>2],i+=t[(3&o[n])<<4|o[n+1]>>4],i+=t[(15&o[n+1])<<2|o[n+2]>>6],i+=t[63&o[n+2]];return r%3===2?i=i.substring(0,i.length-1)+"=":r%3===1&&(i=i.substring(0,i.length-2)+"=="),i},n.decode=function(e){var n,o,r,i,a,s=.75*e.length,c=e.length,l=0;"="===e[e.length-1]&&(s--,"="===e[e.length-2]&&s--);var u=new ArrayBuffer(s),p=new Uint8Array(u);for(n=0;c>n;n+=4)o=t.indexOf(e[n]),r=t.indexOf(e[n+1]),i=t.indexOf(e[n+2]),a=t.indexOf(e[n+3]),p[l++]=o<<2|r>>4,p[l++]=(15&r)<<4|i>>2,p[l++]=(3&i)<<6|63&a;return u}}("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/")},{}],30:[function(t,e){(function(t){function n(t){for(var e=0;e<t.length;e++){var n=t[e];if(n.buffer instanceof ArrayBuffer){var o=n.buffer;if(n.byteLength!==o.byteLength){var r=new Uint8Array(n.byteLength);r.set(new Uint8Array(o,n.byteOffset,n.byteLength)),o=r.buffer}t[e]=o}}}function o(t,e){e=e||{};var o=new i;n(t);for(var r=0;r<t.length;r++)o.append(t[r]);return e.type?o.getBlob(e.type):o.getBlob()}function r(t,e){return n(t),new Blob(t,e||{})}var i=t.BlobBuilder||t.WebKitBlobBuilder||t.MSBlobBuilder||t.MozBlobBuilder,a=function(){try{var t=new Blob(["hi"]);return 2===t.size}catch(e){return!1}}(),s=a&&function(){try{var t=new Blob([new Uint8Array([1,2])]);return 2===t.size}catch(e){return!1}}(),c=i&&i.prototype.append&&i.prototype.getBlob;e.exports=function(){return a?s?t.Blob:r:c?o:void 0}()}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],31:[function(e,n,o){(function(e){!function(r){function i(t){for(var e,n,o=[],r=0,i=t.length;i>r;)e=t.charCodeAt(r++),e>=55296&&56319>=e&&i>r?(n=t.charCodeAt(r++),56320==(64512&n)?o.push(((1023&e)<<10)+(1023&n)+65536):(o.push(e),r--)):o.push(e);return o}function a(t){for(var e,n=t.length,o=-1,r="";++o<n;)e=t[o],e>65535&&(e-=65536,r+=_(e>>>10&1023|55296),e=56320|1023&e),r+=_(e);return r}function s(t){if(t>=55296&&57343>=t)throw Error("Lone surrogate U+"+t.toString(16).toUpperCase()+" is not a scalar value")}function c(t,e){return _(t>>e&63|128)}function l(t){if(0==(4294967168&t))return _(t);var e="";return 0==(4294965248&t)?e=_(t>>6&31|192):0==(4294901760&t)?(s(t),e=_(t>>12&15|224),e+=c(t,6)):0==(4292870144&t)&&(e=_(t>>18&7|240),e+=c(t,12),e+=c(t,6)),e+=_(63&t|128)}function u(t){for(var e,n=i(t),o=n.length,r=-1,a="";++r<o;)e=n[r],a+=l(e);return a}function p(){if(b>=v)throw Error("Invalid byte index");var t=255&y[b];if(b++,128==(192&t))return 63&t;throw Error("Invalid continuation byte")}function f(){var t,e,n,o,r;if(b>v)throw Error("Invalid byte index");if(b==v)return!1;if(t=255&y[b],b++,0==(128&t))return t;if(192==(224&t)){var e=p();if(r=(31&t)<<6|e,r>=128)return r;throw Error("Invalid continuation byte")}if(224==(240&t)){if(e=p(),n=p(),r=(15&t)<<12|e<<6|n,r>=2048)return s(r),r;throw Error("Invalid continuation byte")}if(240==(248&t)&&(e=p(),n=p(),o=p(),r=(15&t)<<18|e<<12|n<<6|o,r>=65536&&1114111>=r))return r;throw Error("Invalid UTF-8 detected")}function h(t){y=i(t),v=y.length,b=0;for(var e,n=[];(e=f())!==!1;)n.push(e);return a(n)}var d="object"==typeof o&&o,m="object"==typeof n&&n&&n.exports==d&&n,g="object"==typeof e&&e;(g.global===g||g.window===g)&&(r=g);var y,v,b,_=String.fromCharCode,w={version:"2.0.0",encode:u,decode:h};if("function"==typeof t&&"object"==typeof t.amd&&t.amd)t(function(){return w});else if(d&&!d.nodeType)if(m)m.exports=w;else{var x={},k=x.hasOwnProperty;for(var E in w)k.call(w,E)&&(d[E]=w[E])}else r.utf8=w}(this)}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],32:[function(t,e){(function(t){var n=/^[\],:{}\s]*$/,o=/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,r=/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,i=/(?:^|:|,)(?:\s*\[)+/g,a=/^\s+/,s=/\s+$/;e.exports=function(e){return"string"==typeof e&&e?(e=e.replace(a,"").replace(s,""),t.JSON&&JSON.parse?JSON.parse(e):n.test(e.replace(o,"@").replace(r,"]").replace(i,""))?new Function("return "+e)():void 0):null}}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],33:[function(t,e,n){n.encode=function(t){var e="";for(var n in t)t.hasOwnProperty(n)&&(e.length&&(e+="&"),e+=encodeURIComponent(n)+"="+encodeURIComponent(t[n]));return e},n.decode=function(t){for(var e={},n=t.split("&"),o=0,r=n.length;r>o;o++){var i=n[o].split("=");e[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}return e}},{}],34:[function(t,e){var n=/^(?:(?![^:@]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,o=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];e.exports=function(t){var e=t,r=t.indexOf("["),i=t.indexOf("]");-1!=r&&-1!=i&&(t=t.substring(0,r)+t.substring(r,i).replace(/:/g,";")+t.substring(i,t.length));for(var a=n.exec(t||""),s={},c=14;c--;)s[o[c]]=a[c]||"";return-1!=r&&-1!=i&&(s.source=e,s.host=s.host.substring(1,s.host.length-1).replace(/;/g,":"),s.authority=s.authority.replace("[","").replace("]","").replace(/;/g,":"),s.ipv6uri=!0),s}},{}],35:[function(t,e){function n(t,e){var n;return n=e?new r(t,e):new r(t)}var o=function(){return this}(),r=o.WebSocket||o.MozWebSocket;e.exports=r?n:null,r&&(n.prototype=r.prototype)},{}],36:[function(t,e){(function(n){function o(t){function e(t){if(!t)return!1;if(n.Buffer&&n.Buffer.isBuffer(t)||n.ArrayBuffer&&t instanceof ArrayBuffer||n.Blob&&t instanceof Blob||n.File&&t instanceof File)return!0;if(r(t)){for(var o=0;o<t.length;o++)if(e(t[o]))return!0}else if(t&&"object"==typeof t){t.toJSON&&(t=t.toJSON());for(var i in t)if(Object.prototype.hasOwnProperty.call(t,i)&&e(t[i]))return!0}return!1}return e(t)}var r=t("isarray");e.exports=o}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{isarray:37}],37:[function(t,e){e.exports=Array.isArray||function(t){return"[object Array]"==Object.prototype.toString.call(t)}},{}],38:[function(t,e){var n=t("global");try{e.exports="XMLHttpRequest"in n&&"withCredentials"in new n.XMLHttpRequest}catch(o){e.exports=!1}},{global:39}],39:[function(t,e){e.exports=function(){return this}()},{}],40:[function(t,e){var n=[].indexOf;e.exports=function(t,e){if(n)return t.indexOf(e);for(var o=0;o<t.length;++o)if(t[o]===e)return o;return-1}},{}],41:[function(t,e,n){var o=Object.prototype.hasOwnProperty;n.keys=Object.keys||function(t){var e=[];for(var n in t)o.call(t,n)&&e.push(n);return e},n.values=function(t){var e=[];for(var n in t)o.call(t,n)&&e.push(t[n]);return e},n.merge=function(t,e){for(var n in e)o.call(e,n)&&(t[n]=e[n]);return t},n.length=function(t){return n.keys(t).length},n.isEmpty=function(t){return 0==n.length(t)}},{}],42:[function(t,e){var n=/^(?:(?![^:@]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,o=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];e.exports=function(t){for(var e=n.exec(t||""),r={},i=14;i--;)r[o[i]]=e[i]||"";return r}},{}],43:[function(t,e,n){(function(e){var o=t("isarray"),r=t("./is-buffer");n.deconstructPacket=function(t){function e(t){if(!t)return t;if(r(t)){var i={_placeholder:!0,num:n.length};return n.push(t),i}if(o(t)){for(var a=new Array(t.length),s=0;s<t.length;s++)a[s]=e(t[s]);return a}if("object"==typeof t&&!(t instanceof Date)){var a={};for(var c in t)a[c]=e(t[c]);return a}return t}var n=[],i=t.data,a=t;return a.data=e(i),a.attachments=n.length,{packet:a,buffers:n}},n.reconstructPacket=function(t,e){function n(t){if(t&&t._placeholder){var r=e[t.num];return r}if(o(t)){for(var i=0;i<t.length;i++)t[i]=n(t[i]);return t}if(t&&"object"==typeof t){for(var a in t)t[a]=n(t[a]);return t}return t}return t.data=n(t.data),t.attachments=void 0,t},n.removeBlobs=function(t,n){function i(t,c,l){if(!t)return t;if(e.Blob&&t instanceof Blob||e.File&&t instanceof File){a++;var u=new FileReader;u.onload=function(){l?l[c]=this.result:s=this.result,--a||n(s)},u.readAsArrayBuffer(t)}else if(o(t))for(var p=0;p<t.length;p++)i(t[p],p,t);else if(t&&"object"==typeof t&&!r(t))for(var f in t)i(t[f],f,t)}var a=0,s=t;i(s),a||n(s)}}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./is-buffer":45,isarray:46}],44:[function(t,e,n){function o(){}function r(t){var e="",o=!1;return e+=t.type,(n.BINARY_EVENT==t.type||n.BINARY_ACK==t.type)&&(e+=t.attachments,e+="-"),t.nsp&&"/"!=t.nsp&&(o=!0,e+=t.nsp),null!=t.id&&(o&&(e+=",",o=!1),e+=t.id),null!=t.data&&(o&&(e+=","),e+=p.stringify(t.data)),u("encoded %j as %s",t,e),e}function i(t,e){function n(t){var n=h.deconstructPacket(t),o=r(n.packet),i=n.buffers;i.unshift(o),e(i)}h.removeBlobs(t,n)}function a(){this.reconstructor=null}function s(t){var e={},o=0;if(e.type=Number(t.charAt(0)),null==n.types[e.type])return l();if(n.BINARY_EVENT==e.type||n.BINARY_ACK==e.type){for(var r="";"-"!=t.charAt(++o)&&(r+=t.charAt(o),o!=t.length););if(r!=Number(r)||"-"!=t.charAt(o))throw new Error("Illegal attachments");e.attachments=Number(r)}if("/"==t.charAt(o+1))for(e.nsp="";++o;){var i=t.charAt(o);if(","==i)break;if(e.nsp+=i,o==t.length)break}else e.nsp="/";var a=t.charAt(o+1);if(""!==a&&Number(a)==a){for(e.id="";++o;){var i=t.charAt(o);if(null==i||Number(i)!=i){--o;break}if(e.id+=t.charAt(o),o==t.length)break}e.id=Number(e.id)}if(t.charAt(++o))try{e.data=p.parse(t.substr(o))}catch(s){return l()}return u("decoded %s as %j",t,e),e}function c(t){this.reconPack=t,this.buffers=[]}function l(){return{type:n.ERROR,data:"parser error"}}var u=t("debug")("socket.io-parser"),p=t("json3"),f=(t("isarray"),t("component-emitter")),h=t("./binary"),d=t("./is-buffer");n.protocol=4,n.types=["CONNECT","DISCONNECT","EVENT","BINARY_EVENT","ACK","BINARY_ACK","ERROR"],n.CONNECT=0,n.DISCONNECT=1,n.EVENT=2,n.ACK=3,n.ERROR=4,n.BINARY_EVENT=5,n.BINARY_ACK=6,n.Encoder=o,n.Decoder=a,o.prototype.encode=function(t,e){if(u("encoding packet %j",t),n.BINARY_EVENT==t.type||n.BINARY_ACK==t.type)i(t,e);else{var o=r(t);e([o])}},f(a.prototype),a.prototype.add=function(t){var e;if("string"==typeof t)e=s(t),n.BINARY_EVENT==e.type||n.BINARY_ACK==e.type?(this.reconstructor=new c(e),0===this.reconstructor.reconPack.attachments&&this.emit("decoded",e)):this.emit("decoded",e);else{if(!d(t)&&!t.base64)throw new Error("Unknown type: "+t);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");e=this.reconstructor.takeBinaryData(t),e&&(this.reconstructor=null,this.emit("decoded",e))}},a.prototype.destroy=function(){this.reconstructor&&this.reconstructor.finishedReconstruction()},c.prototype.takeBinaryData=function(t){if(this.buffers.push(t),this.buffers.length==this.reconPack.attachments){var e=h.reconstructPacket(this.reconPack,this.buffers);return this.finishedReconstruction(),e}return null},c.prototype.finishedReconstruction=function(){this.reconPack=null,this.buffers=[]}},{"./binary":43,"./is-buffer":45,"component-emitter":9,debug:10,isarray:46,json3:47}],45:[function(t,e){(function(t){function n(e){return t.Buffer&&t.Buffer.isBuffer(e)||t.ArrayBuffer&&e instanceof ArrayBuffer}e.exports=n}).call(this,"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],46:[function(t,e){e.exports=t(37)},{}],47:[function(e,n,o){!function(e){function n(t){if(n[t]!==a)return n[t];var e;if("bug-string-char-index"==t)e="a"!="a"[0];else if("json"==t)e=n("json-stringify")&&n("json-parse");else{var o,r='{"a":[1,true,false,null,"\\u0000\\b\\n\\f\\r\\t"]}';if("json-stringify"==t){var i=u.stringify,c="function"==typeof i&&p;if(c){(o=function(){return 1}).toJSON=o;try{c="0"===i(0)&&"0"===i(new Number)&&'""'==i(new String)&&i(s)===a&&i(a)===a&&i()===a&&"1"===i(o)&&"[1]"==i([o])&&"[null]"==i([a])&&"null"==i(null)&&"[null,null,null]"==i([a,s,null])&&i({a:[o,!0,!1,null,"\0\b\n\f\r    "]})==r&&"1"===i(null,o)&&"[\n 1,\n 2\n]"==i([1,2],null,1)&&'"-271821-04-20T00:00:00.000Z"'==i(new Date((-864e13)))&&'"+275760-09-13T00:00:00.000Z"'==i(new Date(864e13))&&'"-000001-01-01T00:00:00.000Z"'==i(new Date((-621987552e5)))&&'"1969-12-31T23:59:59.999Z"'==i(new Date((-1)))}catch(l){c=!1}}e=c}if("json-parse"==t){var f=u.parse;if("function"==typeof f)try{if(0===f("0")&&!f(!1)){o=f(r);var h=5==o.a.length&&1===o.a[0];if(h){try{h=!f('" "')}catch(l){}if(h)try{h=1!==f("01")}catch(l){}if(h)try{h=1!==f("1.")}catch(l){}}}}catch(l){h=!1}e=h}}return n[t]=!!e}var r,i,a,s={}.toString,c="function"==typeof t&&t.amd,l="object"==typeof JSON&&JSON,u="object"==typeof o&&o&&!o.nodeType&&o;u&&l?(u.stringify=l.stringify,u.parse=l.parse):u=e.JSON=l||{};var p=new Date((-0xc782b5b800cec));try{p=-109252==p.getUTCFullYear()&&0===p.getUTCMonth()&&1===p.getUTCDate()&&10==p.getUTCHours()&&37==p.getUTCMinutes()&&6==p.getUTCSeconds()&&708==p.getUTCMilliseconds()}catch(f){}if(!n("json")){var h="[object Function]",d="[object Date]",m="[object Number]",g="[object String]",y="[object Array]",v="[object Boolean]",b=n("bug-string-char-index");if(!p)var _=Math.floor,w=[0,31,59,90,120,151,181,212,243,273,304,334],x=function(t,e){return w[e]+365*(t-1970)+_((t-1969+(e=+(e>1)))/4)-_((t-1901+e)/100)+_((t-1601+e)/400)};(r={}.hasOwnProperty)||(r=function(t){var e,n={};return(n.__proto__=null,n.__proto__={toString:1},n).toString!=s?r=function(t){var e=this.__proto__,n=t in(this.__proto__=null,this);return this.__proto__=e,n}:(e=n.constructor,r=function(t){var n=(this.constructor||e).prototype;return t in this&&!(t in n&&this[t]===n[t])}),n=null,r.call(this,t)});var k={"boolean":1,number:1,string:1,undefined:1},E=function(t,e){var n=typeof t[e];return"object"==n?!!t[e]:!k[n]};if(i=function(t,e){var n,o,a,c=0;(n=function(){this.valueOf=0}).prototype.valueOf=0,o=new n;for(a in o)r.call(o,a)&&c++;return n=o=null,c?i=2==c?function(t,e){var n,o={},i=s.call(t)==h;for(n in t)i&&"prototype"==n||r.call(o,n)||!(o[n]=1)||!r.call(t,n)||e(n)}:function(t,e){var n,o,i=s.call(t)==h;for(n in t)i&&"prototype"==n||!r.call(t,n)||(o="constructor"===n)||e(n);(o||r.call(t,n="constructor"))&&e(n)}:(o=["valueOf","toString","toLocaleString","propertyIsEnumerable","isPrototypeOf","hasOwnProperty","constructor"],i=function l(t,e){var n,l,i=s.call(t)==h,a=!i&&"function"!=typeof t.constructor&&E(t,"hasOwnProperty")?t.hasOwnProperty:r;for(n in t)i&&"prototype"==n||!a.call(t,n)||e(n);for(l=o.length;n=o[--l];a.call(t,n)&&e(n));}),i(t,e)},!n("json-stringify")){var S={92:"\\\\",34:'\\"',8:"\\b",12:"\\f",10:"\\n",13:"\\r",9:"\\t"},T="000000",C=function(t,e){return(T+(e||0)).slice(-t)},A="\\u00",N=function(t){var e,n='"',o=0,r=t.length,i=r>10&&b;for(i&&(e=t.split(""));r>o;o++){var a=t.charCodeAt(o);switch(a){case 8:case 9:case 10:case 12:case 13:case 34:case 92:n+=S[a];break;default:if(32>a){n+=A+C(2,a.toString(16));break}n+=i?e[o]:b?t.charAt(o):t[o]}}return n+'"'},O=function z(t,e,n,o,c,l,u){var p,f,h,b,w,k,E,S,T,A,O,R,B,j,I,D;try{p=e[t]}catch(L){}if("object"==typeof p&&p)if(f=s.call(p),f!=d||r.call(p,"toJSON"))"function"==typeof p.toJSON&&(f!=m&&f!=g&&f!=y||r.call(p,"toJSON"))&&(p=p.toJSON(t));else if(p>-1/0&&1/0>p){if(x){for(w=_(p/864e5),h=_(w/365.2425)+1970-1;x(h+1,0)<=w;h++);for(b=_((w-x(h,0))/30.42);x(h,b+1)<=w;b++);w=1+w-x(h,b),k=(p%864e5+864e5)%864e5,E=_(k/36e5)%24,S=_(k/6e4)%60,T=_(k/1e3)%60,A=k%1e3}else h=p.getUTCFullYear(),b=p.getUTCMonth(),w=p.getUTCDate(),E=p.getUTCHours(),S=p.getUTCMinutes(),T=p.getUTCSeconds(),A=p.getUTCMilliseconds();p=(0>=h||h>=1e4?(0>h?"-":"+")+C(6,0>h?-h:h):C(4,h))+"-"+C(2,b+1)+"-"+C(2,w)+"T"+C(2,E)+":"+C(2,S)+":"+C(2,T)+"."+C(3,A)+"Z"}else p=null;if(n&&(p=n.call(e,t,p)),null===p)return"null";if(f=s.call(p),f==v)return""+p;if(f==m)return p>-1/0&&1/0>p?""+p:"null";if(f==g)return N(""+p);if("object"==typeof p){for(j=u.length;j--;)if(u[j]===p)throw TypeError();if(u.push(p),O=[],I=l,l+=c,f==y){for(B=0,j=p.length;j>B;B++)R=z(B,p,n,o,c,l,u),O.push(R===a?"null":R);D=O.length?c?"[\n"+l+O.join(",\n"+l)+"\n"+I+"]":"["+O.join(",")+"]":"[]"}else i(o||p,function(t){var e=z(t,p,n,o,c,l,u);e!==a&&O.push(N(t)+":"+(c?" ":"")+e)}),D=O.length?c?"{\n"+l+O.join(",\n"+l)+"\n"+I+"}":"{"+O.join(",")+"}":"{}";return u.pop(),D}};u.stringify=function(t,e,n){var o,r,i,a;if("function"==typeof e||"object"==typeof e&&e)if((a=s.call(e))==h)r=e;else if(a==y){i={};for(var c,l=0,u=e.length;u>l;c=e[l++],a=s.call(c),(a==g||a==m)&&(i[c]=1));}if(n)if((a=s.call(n))==m){if((n-=n%1)>0)for(o="",n>10&&(n=10);o.length<n;o+=" ");}else a==g&&(o=n.length<=10?n:n.slice(0,10));return O("",(c={},c[""]=t,c),r,i,o,"",[])}}if(!n("json-parse")){var R,B,j=String.fromCharCode,I={92:"\\",34:'"',47:"/",98:"\b",116:" ",110:"\n",102:"\f",114:"\r"},D=function(){throw R=B=null,SyntaxError()},L=function(){for(var t,e,n,o,r,i=B,a=i.length;a>R;)switch(r=i.charCodeAt(R)){case 9:case 10:case 13:case 32:R++;break;case 123:case 125:case 91:case 93:case 58:case 44:return t=b?i.charAt(R):i[R],R++,t;case 34:for(t="@",R++;a>R;)if(r=i.charCodeAt(R),32>r)D();else if(92==r)switch(r=i.charCodeAt(++R)){case 92:case 34:case 47:case 98:case 116:case 110:case 102:case 114:t+=I[r],R++;break;case 117:for(e=++R,n=R+4;n>R;R++)r=i.charCodeAt(R),r>=48&&57>=r||r>=97&&102>=r||r>=65&&70>=r||D();t+=j("0x"+i.slice(e,R));break;default:D()}else{if(34==r)break;for(r=i.charCodeAt(R),e=R;r>=32&&92!=r&&34!=r;)r=i.charCodeAt(++R);t+=i.slice(e,R)}if(34==i.charCodeAt(R))return R++,t;D();default:if(e=R,45==r&&(o=!0,r=i.charCodeAt(++R)),r>=48&&57>=r){for(48==r&&(r=i.charCodeAt(R+1),r>=48&&57>=r)&&D(),o=!1;a>R&&(r=i.charCodeAt(R),r>=48&&57>=r);R++);if(46==i.charCodeAt(R)){for(n=++R;a>n&&(r=i.charCodeAt(n),r>=48&&57>=r);n++);n==R&&D(),R=n}if(r=i.charCodeAt(R),101==r||69==r){for(r=i.charCodeAt(++R),(43==r||45==r)&&R++,n=R;a>n&&(r=i.charCodeAt(n),r>=48&&57>=r);n++);n==R&&D(),R=n}return+i.slice(e,R)}if(o&&D(),"true"==i.slice(R,R+4))return R+=4,!0;if("false"==i.slice(R,R+5))return R+=5,!1;if("null"==i.slice(R,R+4))return R+=4,null;D()}return"$"},P=function F(t){var e,n;if("$"==t&&D(),"string"==typeof t){if("@"==(b?t.charAt(0):t[0]))return t.slice(1);if("["==t){for(e=[];t=L(),"]"!=t;n||(n=!0))n&&(","==t?(t=L(),"]"==t&&D()):D()),","==t&&D(),e.push(F(t));return e}if("{"==t){for(e={};t=L(),"}"!=t;n||(n=!0))n&&(","==t?(t=L(),"}"==t&&D()):D()),(","==t||"string"!=typeof t||"@"!=(b?t.charAt(0):t[0])||":"!=L())&&D(),e[t.slice(1)]=F(L());return e}D()}return t},U=function(t,e,n){var o=M(t,e,n);o===a?delete t[e]:t[e]=o},M=function(t,e,n){var o,r=t[e];if("object"==typeof r&&r)if(s.call(r)==y)for(o=r.length;o--;)U(r,o,n);else i(r,function(t){U(r,t,n)});return n.call(t,e,r)};u.parse=function(t,e){var n,o;return R=0,B=""+t,n=P(L()),"$"!=L()&&D(),R=B=null,e&&s.call(e)==h?M((o={},o[""]=n,o),"",e):n}}}c&&t(function(){return u})}(this)},{}],48:[function(t,e){function n(t,e){var n=[];e=e||0;for(var o=e||0;o<t.length;o++)n[o-e]=t[o];return n}e.exports=n},{}]},{},[1])(1)}),window.console=window.console||{log:function(){},error:function(){},warn:function(){}},function(t,e,o){function r(t){var e=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");return e.appendChild(n),n.type="text/css",n.styleSheet?n.styleSheet.cssText=t:n.appendChild(document.createTextNode(t)),n}function i(t,e){try{return decodeURIComponent(t).match(/https?:\/\/(.+?)(\/|$)/)[1]===e}catch(n){return!1}}function a(){Z.pop&&Z.pop.auto_popup&&Z.pop.auto_popup.enabled&&Z.pop.auto_popup.on_receive_message&&!Mt.opened&&Mt.show()}function s(){if(!Mt.opened&&at&&!Et&&Z.pop&&Z.pop.auto_popup&&Z.pop.auto_popup.enabled&&Z.pop.auto_popup.interval){var t=setInterval(function(){Mt.opened||Mt.show()},1e3*Z.pop.auto_popup.interval);return function(){clearInterval(t)}}return function(){}}function c(t){var e,n=Z,o=Dt.extend({color:"#307AE8",isInvite:!0,onlineText:qt.g("tips.callAgent"),offlineText:qt.g("tips.leaveMsg")},it?{mode:"blank",pos_flag:"crb",css:{"margin-top":"18px","margin-bottom":"18px","margin-left":"18px","margin-right":"18px"}}:{mode:"inner",pos_flag:"hrb",css:{"margin-top":"3px","margin-bottom":"3px","margin-left":"30px","margin-right":"30px"}});if(n.code||n.link){it&&Dt.extend(n,n.mobile),n.pop=n.pop||{},n.panel=n.panel||{},n.visitor_no=n.visitor_no||{},e=Dt.urlParams(n.link),t&&(it&&Dt.extend(t,t.mobile),n.pop=Dt.extend(t.pop||{},n.pop),n.panel=Dt.extend(t.panel||{},n.panel),n.visitor_no=Dt.extend(t.visitor_no||{},n.visitor_no),n=Dt.extend(t,n)),n=Dt.extend(o,n,e),"srb"===n.pos_flag&&(n.pos_flag="hrb"),"srm"===n.pos_flag&&(n.pos_flag="vrm");var r=location.href.indexOf(G+"/im_client/preview")>-1,a=e,s={},c="";if(et?i(et,location.host)||(Dt.store("first_access",et),Dt.store("first_url",location.href),c=location.href):(Dt.store("first_access",""),Dt.store("first_url",location.href),c=location.href),Dt.extend(a,{cur_title:gt,web_plugin_id:V}),n.uba)for(var l in n.uba)["app_key"].indexOf(l)>-1&&(a["uba["+l+"]"]=encodeURIComponent(n.uba[l]));if(!r){var u=n.src_url?encodeURIComponent(n.src_url):Dt.store("first_access")||et,p=n.cur_url?encodeURIComponent(n.cur_url):tt,f=encodeURIComponent(n.pre_url||Dt.store("first_url")||c);Dt.extend(a,{src_url:u,cur_url:p,pre_url:f})}return n.linkParams=a,Z=n,h({linkParams:a,feedParams:s}),n}}function l(t){Rt.id=t.agent_id,Rt.group_id=t.group_id,Rt.avatar=G+"/im_client/images/agent_avatar.png"}function u(){if(et){var t=Dt.urlParams(decodeURIComponent(et))||{},e=Dt.urlParams(decodeURIComponent(Z.src_url||Z.link))||{};return t.xst||e.xst||""}return""}function p(t){var e,n=u();return n?(Dt.jsonp(G+"/im/generate_webtoken_sign?xst="+n,function(o){return e=!0,o&&o.signature&&o.nonce&&o.timestamp&&o.web_token&&o.customer_token&&(Z.customer={signature:o.signature,nonce:o.nonce,timestamp:o.timestamp,web_token:o.web_token,customer_token:o.customer_token},Z.xst=n),t&&t(!0)}),void setTimeout(function(){!e&&t&&t(),e=!0},2e3)):t&&t()}function f(t,e,n){var o=Z.link,r=Z.customer||{},i=Z.ticket||{},a=Z.product||{},s=g(r,o,n),c=y(Z.im_client_valid),l=v(a),u=Z.robot||{},p=Z.encrypt||!1;Z.session_key&&(o+="&session_key="+Z.session_key),Rt.id&&(o+="&agent_id="+Rt.id),Rt.group_id&&(o+="&group_id="+Rt.group_id),nt&&(o+="&udesk_wd="+nt),Q&&(o+="&_INVITE_USER_KEY="+Q),Rt&&!Rt.online&&(o+="&free=noAgent"),Z.h5push&&(o+="&h5push="+Z.h5push),Z.h5mode&&(o+="&udesk_h5mode=1"),(Z.hide_product||Z.udesk_hide_product)&&(o+="&udesk_hide_product=1");for(var f in r){var h=r[f]||"",d=f&&"nonce"!==f&&"timestamp"!==f&&"web_token"!==f&&"signature"!==f&&"encryption_algorithm"!==f;d&&(o+="&"+f+"="+encodeURIComponent(h))}for(var f in i){var m=i[f]||"";f&&(o+="&"+f+"="+encodeURIComponent(m))}for(var f in u)o+="&robot_"+f+"="+u[f];if(o=o+s+c+l,"autoInvite"===t&&(o+="&sender=sys"),Z.xst&&(o+="&xst="+Z.xst),Z.src&&(o+="&chat_src="+Z.src),p&&e){var b="",_=o.split("?")[1];_&&(b=Dt.encodeBase64(encodeURIComponent(_)),o=o.split("?")[0]+"?ud$"+b)}return o}function h(t){t=t||{};var e,n=Z,o=G+"/im_client"+(n.noCDN?"_nocdn":"")+"/",r=o+"feedback.html",i=Dt.extend(Dt.urlParams(n.link),n.linkParams,t.linkParams),a=Dt.extend({},t.feedParams);n.channel&&(i.channel=n.channel),n.language&&(i.language=n.language,a.language=n.language),"inner"===n.mode&&(i.currentMode=n.mode,a.currentMode=n.mode),o+="?"+Dt.urlString(i),e=Dt.urlString(a),e&&(r+="?"+e),Dt.extend(n,{link:o,feedbackLink:r})}function d(){var t={},e=Z.pop.direction||"top";return t[e]=!0,t}function g(t,e,n){var o="";return n?(t.nonce&&(null==e?void 0:e.indexOf("&nonce"))===-1&&(o+="&nonce="+t.nonce),t.timestamp&&(null==e?void 0:e.indexOf("&timestamp"))===-1&&(o+="&timestamp="+t.timestamp),t.web_token&&(null==e?void 0:e.indexOf("&web_token"))===-1&&(o+="&web_token="+t.web_token),t.signature&&(null==e?void 0:e.indexOf("&signature"))===-1&&(o+="&signature="+t.signature),t.encryption_algorithm&&(null==e?void 0:e.indexOf("&encryption_algorithm"))===-1&&(o+="&encryption_algorithm="+t.encryption_algorithm)):(t.nonce&&(o+="&nonce="+t.nonce),t.timestamp&&(o+="&timestamp="+t.timestamp),t.web_token&&(o+="&web_token="+t.web_token),t.signature&&(o+="&signature="+t.signature),t.encryption_algorithm&&(o+="&encryption_algorithm="+t.encryption_algorithm)),o}function y(t){if(!t)return"";var e="";return t.v_nonce&&(e+="&v_nonce="+t.v_nonce),t.v_timestamp&&(e+="&v_timestamp="+t.v_timestamp),t.v_signature&&(e+="&v_signature="+t.v_signature),e}function v(t){var e="";for(var n in t)e+="&product_"+n+"="+encodeURIComponent(t[n]);return e}function b(t){Dt.jsonp(G+"/agents/free?im_web_plugin_id="+(V||"")+"&session_key="+(Z.session_key||""),function(e){Rt.online=!!e.available,at=!!e.chatting,t&&t()})}function _(){if(Rt.online&&Ut.autoGen){var t=Ut.imgEl;if(t)Z.onlineImgUrl&&(t.src=Z.onlineImgUrl);else{var e=Z.pos_flag||[],n=e[0];if("v"===n){var o=Z.onlineText.replace(/^\s+|\s+$/g,"").replace(/ +/g,'<b style="display:inline-block;height:5px;line-height:5px;">&nbsp;</b>');Lt.html(Ut.textEl,o||Z.onlineText)}else Lt.text(Ut.textEl,Z.onlineText)}}else if(!Rt.online&&Ut.autoGen){var t=Ut.imgEl;if(t)Z.offlineImgUrl&&(t.src=Z.offlineImgUrl);else{var e=Z.pos_flag||[],n=e[0];if("v"===n){var o=Z.offlineText.replace(/^\s+|\s+$/g,"").replace(/ +/g,'<b style="display:inline-block;height:5px;line-height:5px;">&nbsp;</b>');Lt.html(Ut.textEl,o||Z.offlineText)}else Lt.text(Ut.textEl,Z.offlineText)}}}function w(){Mt.loadSrc("autoInvite"),jt?Dt.store("on_auto_chat")||(Dt.store("on_auto_chat",+new Date),Ut.clickHandler("autoInvite")):Ut.clickHandler("autoInvite")}function x(){if(jt){var t=Dt.store("on_auto_chat")||0,e=+new Date-t;e>60*parseInt(jt)*1e3&&Dt.store("on_auto_chat","")}}function k(t,e){try{var n=navigator.mimeTypes;for(var o in n)if(n[o][t]==e)return!0;return!1}catch(r){return!1}}function E(t){var e,n=Z||{},o=X+"key?_=";if(n.isInvite&&(o+="&is_invite=1"),n.desc&&(o+="&desc="+n.desc),Q&&(o+="&key="+Q),o+="&"+ot+"&title="+gt+"&scaleScreen="+(window.screen.width+"*"+window.screen.height),Ct.indexOf("chrome")>-1){var r=k("type","application/vnd.chromium.remoting-viewer");r&&(o+="&browser_name=360")}Dt.jsonp(o,function(n){!e&&t&&t(n),e=!0}),setTimeout(function(){!e&&t&&t(),e=!0},2e3)}function S(t){var e=Z.customer||{},n=G+"/spa1/im_web_plugins/"+(V||0)+"/out_config?company_code="+Z.code+"&language="+(Z.language||"")+"&session_key="+(Z.session_key||"");e.nonce&&e.timestamp&&e.web_token&&e.signature&&(n+="&nonce="+e.nonce+"&timestamp="+e.timestamp+"&web_token="+e.web_token+"&signature="+e.signature,e.encryption_algorithm&&(n=n+"&encryption_algorithm="+e.encryption_algorithm)),Z.group_id&&(n+="&_log_group_id="+Z.group_id),Z.agent_id&&(n+="&_log_agent_id="+Z.agent_id),Dt.jsonp(n,function(e){e=e||{},xt=e.offline||{},Bt.window=e.window,Bt.im_client_valid_enable=e.im_client_valid_enable,Bt.vistor=e.vistor||{},It.config=e,t(e.data)})}function T(){return Mt&&Mt.iframeEl?Mt.iframeEl.contentWindow:null}function C(t,e,n){var o=T();t=t||"chatting";var r={type:t,agent_id:Rt.id};e&&(r.chat_creator="agent"),ut&&(r.invited=!0),n&&(r.reload=!0);try{o?Dt.sendMessage(r,o):setTimeout(function(){C(t,e)},50)}catch(i){}}function A(e){ct?"force_chat"===e&&C("forceChat",!0):(ct=!0,C("chatting","force_chat"===e,!0)),setTimeout(function(){Dt.store("on_go_chat",""),Dt.store("on_force_chat","")},1e4),zt.stopAutoStrategy(),"inner"===Z.mode?(Mt.show(e),C("autoSize")):it?("force_chat"===e&&rt&&rt.emit("force_chatting"),p(function(n){t.location.href=f(e,!1,n)})):O(e)}function N(t){C("chatting",!1,!0),Mt._show(t),C("autoSize")}function O(t){var e=Z.window||{},n=(""+(e.width||780)).replace("px",""),o=(""+(e.height||560)).replace("px","");if("force_chat"===t){var r=100*Math.ceil(30*Math.random());setTimeout(function(){Dt.store("on_go_chat")||(Dt.store("on_go_chat",+new Date),rt&&rt.emit("force_chatting"),R(n,o))},r)}else"autoInvite"===t?R(n,o,t):R(n,o)}function R(e,n,r){Ut.clearUnread(!0);var i,a="newtab"===Z.mode;p(function(s){i=f(r,!0,s),a?t.open(i):t.open(i,"udesk_im",it?o:"width="+e+",height="+n+",resizable=yes")})}function B(t,e){if(!t)return"";e=e||"";var n,o=Dt.urlParams(t),r=Dt.urlParams(e),i={baidu:{key:"wd|word"},bing:{key:"q|pq"},sogou:{key:"keyword|query"},yahoo:{key:"p"}},a=e.split("/")[2]||"",s=a.split("."),c=s[s.length-2],l=i[c];if(o.udesk_wd)return o.udesk_wd;if(l){n=l.key.split("|");for(var u=0,p=n.length;u<p;u++)if(r[n[u]])return r[n[u]]}else if(r.q)return r.q;return""}function j(t){if(t.count){for(var e=t.count||0,n=t.im_logs||[],o=0,r=n.length;o<r;o++)!function(t){Ht.push(setTimeout(function(){var o,r=n[t],i=Dt.parseJSON(r.content);switch(i.type){case"redirect":case"timeout":return;case"file":case"audio":o="［"+qt.g("tips.file")+"］";break;case"image":o="［"+qt.g("tips.image")+"］";break;case"message":o=i.data?i.data.content:qt.g("tips.newMsgTitle"),o&&o.search("emoji")>-1&&(o=o.replace(/\[emoji([^\].]+)\]/g,'<span class="udesk_im_client_emoji udesk_im_client_emoji$1"></span>'));break;default:o=qt.g("tips.newMsgTitle")}Ut.unreadHandler({msg:o,type:i.type,count:e})},2e3*t))}(o);Ut.unreadHandler({count:e})}}function I(t){var e=t;return"string"==typeof t&&(e={},e[t]=arguments[1]),Dt.extend(Tt,e)}function D(t){var e=Z.customer||{},n=G+"/spa1/im_user/chatting?company_code="+Z.code+"&language="+(Z.language||"")+"&session_key="+(Z.session_key||"");e.nonce&&e.timestamp&&e.web_token&&e.signature&&(n+="&nonce="+e.nonce+"&timestamp="+e.timestamp+"&web_token="+e.web_token+"&signature="+e.signature,e.encryption_algorithm&&(n=n+"&encryption_algorithm="+e.encryption_algorithm)),Z.group_id&&(n+="&group_id="+Z.group_id),
Z.agent_id&&(n+="&agent_id="+Z.agent_id),Dt.jsonp(n,function(e){e=e||{},t&&t(e.chatting)})}function L(t){Ft.eachPushFn(t),Dt.extend(Z,t),h(Z),lt||Mt.opened||(ct=!1),Mt.loadSrc(),_t&&"function"==typeof t.onReady&&t.onReady()}function P(){var e="不能为空",n=function(t){},o=function(t){t=t||{};var o=t.type,r=t.data,i=Z.customer||{};if(!r)return void n("data"+e);switch(o){case"product":return{url:X+"vistor_data_trace",data:{key:Q,code:Z.code,type:o,data:encodeURIComponent(JSON.stringify(r))},valid:function(){var t=[];return It.config.behavior_trace?(!r.name&&t.push("商品名称:"+e),!t.length||(n(t.join("、")),!1)):(n("未开通商品浏览轨迹"),!1)}};case"order":return{url:G+"/business_api/v1/web_customer_orders",data:{nonce:i.nonce,timestamp:i.timestamp,web_token:i.web_token,customer_token:i.customer_token,signature:i.signature,encryption_algorithm:i.encryption_algorithm,web_plugin_id:V,order:encodeURIComponent(JSON.stringify(r))},valid:function(){var t=[];return It.config.customer_order?i.signature?(!r.order_no&&t.push("订单编号:"+e),!r.price&&t.push("订单金额:"+e),!t.length||(n(t.join("、")),!1)):(n("请传入客户身份认证请求参数"),!1):(n("未开通订单事件"),!1)},cb:function(t){1e3!==t.code&&n(t.code_message)}};default:n("type参数错误")}},r={init:function(){Z.manualInit&&F()},setProduct:function(t){t=t||Z.product,t&&Dt.sendMessage({type:"setProduct",product:t},Mt.iframeEl.contentWindow)},showPanel:function(){A()},hidePanel:function(){Mt.hide()},isDrag:function(){document.getElementsByClassName("udesk-client-btn")[0].classList.add("drag")},noDrag:function(){document.getElementsByClassName("udesk-client-btn")[0].classList.remove("drag")},clearProduct:function(){Dt.sendMessage({type:"clearProduct"},Mt.iframeEl.contentWindow)},getVisitorNo:function(){return bt},dataTrace:function(t,e){var n=o({type:t,data:e});n&&n.valid(t,e)&&Dt.jsonp(n.url+"?"+Dt.urlString(n.data),function(t){n.cb&&n.cb(t)})},updateConfig:function(t){try{"object"==typeof Dt.get(t,"panel.css")&&(Mt.panelEl.style.cssText=Lt.toCssText(Dt.assign(t.panel.css,{display:Mt.panelEl.style.display,visibility:Mt.panelEl.style.visibility,bottom:Mt.panelEl.style.bottom,zIndex:Mt.panelEl.style.zIndex}))),"function"==typeof Dt.get(t,"panel.onToggle")&&(Ft.off("panel.onToggle",Dt.get(Z,"panel.onToggle")),Ft.pushFn("panel.onToggle",Dt.get(t,"panel.onToggle")))}catch(e){}},isPanelOpened:function(){try{return Mt.opened}catch(t){}}};t[t.UdeskApiObject]=function(t){if(t){var e,n=arguments,o=t,i=!0;if("string"==typeof t&&r.hasOwnProperty(t))return r[n[0]].apply(null,[].slice.call(n,1));"object"!=typeof t&&(o={},o[n[0]]=n[1]),(o.channel||o.language||o.customer)&&(e=!0),(o.agent_id||o.group_id)&&(wt?(i=!1,D(function(t){t?(delete o.group_id,delete o.agent_id,e&&L(o)):(l(o),L(o))})):(l(o),e=!0)),i&&e&&L(o)}},Tt=t[t.UdeskApiObject],I(r)}function U(){q=Dt.isTestENV(),H=Dt.ifIE(),tt=encodeURIComponent(location.href),et=encodeURIComponent(Dt.getReferrer()||""),gt=encodeURIComponent(e.title)||"",headEl=Lt.find("head",e)[0]||e.head||e.getElementsByTagName("head")[0]||{},Q=Dt.store("userkey"),ft=Lt.find("body",e)[0]||e.body||{},yt=(e.documentElement||ft).clientWidth||1366,vt=(e.documentElement||ft).clientHeight,it=yt<=768,Q=Dt.store("userkey"),Dt.store("on_go_chat",""),Dt.store("on_force_chat",""),Dt.platform({iphone:function(){it=!0}}),nt=B(tt,decodeURIComponent(Dt.store("first_access")||et||""));var t={};if(Tt&&Dt.each(Tt.d,function(e){var n=e[0];"object"!=typeof e[0]&&(n={},n[e[0]]=e[1]),Ft.eachPushFn(n),Dt.extend(t,n)}),t.link){var n=Dt.parseUrl(t.link),o=Dt.urlParams(t.link);n.protocol&&(At=n.protocol),G=At+"//"+n.host,V=o.web_plugin_id}t.manualInit=t.manualInit||t.manueInit||!1,jt=t.auto_invite_interval,Z=t}function M(){var t=document.createElement("link"),e=G+"/im_client/css/ui/emotion.css";t.type="text/css",t.rel="stylesheet",t.href=e,headEl.appendChild(t);var n=document.createElement("style"),o='url("'+G+'/im_client/images/im-emoji-big-1.png")',r="#udesk_pop_dialog .udesk_im_client_emoji { background-image: "+o+"}";n.type="text/css",n.rel="stylesheet";try{n.appendChild(document.createTextNode(r))}catch(i){n.styleSheet.cssText=r}headEl.appendChild(n)}function z(){U(),x();var t=function(t){var e=c(t),n=Bt.vistor||{};l(e),ot="code="+(e.code||"")+"&url="+tt+"&referrer="+(e.src_url||et)+"&keyword="+nt,J="https:"===At?"6002":"6001",W=(q?G.replace(At+"//",""):"basevistor.udesk.cn")+":"+J,X=(n[At.replace(":","")]||At+"//"+W)+"/customerApi/",Y=(n["https:"===At?"wss":"ws"]||At+"//"+W)+"/customer"};Z.code&&Z.link&&S(function(e){t(e),P(),M();var n=Z.manualInit;e.im_guest_enable&&!Z.noVisitor?(Q&&!n&&F(),E(function(t){t=t||{},t.key&&(Q=t.key,Dt.store("userkey",Q),zt.init(),bt=t.visitor_no,Ut.updateVisitorNo(bt)),!n&&F()})):!n&&F(),n&&(_t=!0,Ft.init(),Ft.execFn("onReady"))})}function F(){st||(st=!0,Lt.ready(function(){ht=Lt.createEl({id:St+"container"}),Dt.isEmptyObj(ft)&&(ft=Lt.find("body",e)[0]||e.body||{}),ft.appendChild(ht),Ut.init(),Mt.init(),Ft.init(),Q&&zt.init(),j(xt),b(function(){Mt.loadSrc(),_(),Rt.online&&zt.startAutoStrategy(),Z.manualInit||(_t=!0,Ft.execFn("onReady"))})}))}var q,H,W,J,X,Y,G,V,Z,Q,tt,et,nt,ot,rt,it,at,st,ct,lt,ut,pt,ft,ht,dt,mt,gt,yt,vt,bt,_t,wt,xt,kt,Et,St="udesk_",Tt=t[t.UdeskApiObject],Ct=navigator.userAgent.toLowerCase(),At="file:"===location.protocol?"http:":location.protocol,Nt=0,Ot=999999999,Rt={},Bt={},jt=0,It={config:{}};if(!t.__udeskApiInit){t.__udeskApiInit=!0;var Dt={isTestENV:function(){return"file:"===location.protocol||/(ud|tiyanudesk|(udesk(.+)?)).com$ |localhost/i.test(location.host)},getCurrentStaticUrl:function(){var t=location.hostname,e=/\.(.*)\.udesk.cn/,n=t.match(e),o=n&&n[1];if(!o)if(/udesk.cn/.test(t))o="";else{if(/localhost|local/.test(t))return"static-ud.udesk.cn";if(/tryudesk/.test(t)){var r=t.match(/\.(.*).tryudesk.cn/);return"static-ud."+r+".tryudesk.com"}}return"static-ud."+(o?o+".":"")+"udesk.cn"},jsonp:function(n,o){if(n){var r=St+"jsonp"+Nt++;n+=(n.indexOf("?")>-1?"&":"?")+"callback="+r;var i=e.createElement("script");headEl.appendChild(i),t[r]=function(t){o&&o(t)},i.src=n,i.onerror=function(){o&&o()}}},replaceTpl:function(t,e){if(!t)return"";if(!e)return t;var n=t;"function"==typeof t&&(n=t(e));for(var o in e)n=n.replace(new RegExp("{"+o+"}","gm"),e[o]||"");return n},parseJSON:function(e){try{return t.JSON.parse(e)}catch(n){}return e},extend:function(t){return t?(Dt.each(arguments,function(e,n){if(n>0&&e)for(var o in e)t[o]=e[o]}),t):t},each:function(t,e){if(t){var n=t.length;if(n!==o)for(var r=0;r<n&&!1!==e(t[r],r);r++);else for(var i in t)if(!1===e(t[i],i))break}},isEmptyObj:function(t){if(!t)return!1;for(var e in t)return!1;return!0},getReferrer:function(){var e;try{e=t.top.document.referrer}catch(n){try{e=t.parent.document.referrer}catch(n){}}if(e||(e=document.referrer),!e&&t.opener)try{e=t.opener.location.href}catch(n){}return e||""},messageListener:function(e){t.addEventListener?t.addEventListener("message",function(t){var n=Dt.parseJSON(t.data)||{};e&&e(n)}):t.attachEvent("onmessage",function(t){var n=Dt.parseJSON(t.data)||{};e&&e(n)})},sendMessage:function(t,e){try{t=JSON.stringify(t)||"",e.postMessage(t,G)}catch(n){}},store:function Wt(e,n){e="UDESK_"+e;var Wt=t.localStorage;if(Wt)try{if(n===o)return Wt[e]||"";Wt[e]=n||""}catch(r){}},sessionStore:function(e,n){e="UDESK_"+e;var r=t.sessionStorage;if(r)try{if(n===o)return r[e]||"";r[e]=n||""}catch(i){}},storageListener:function(e){try{t.addEventListener?t.addEventListener("storage",function(t){e&&e(t)}):document.attachEvent&&!K.Browser.opera?document.attachEvent("onstorage",function(t){e&&e(t)}):t.attachEvent("onstorage",function(t){e&&e(t)})}catch(n){}},urlParams:function(t){var e,n={};if(!t)return n;if(e=t.match(new RegExp("[?&][^?&]+=[^?&]+","g")))for(var o=0,r=e.length;o<r;o++){var i,a,s;try{i=e[o].substring(1).split("="),a=decodeURIComponent(i[0]),s=decodeURIComponent(i[1]),n[a]=s}catch(c){i&&i[0]&&(n[i[0]]=i[1]||"")}}return n},urlString:function(t,e){var n="";if(t)for(var o in t)n+="&"+o+"="+(e?encodeURIComponent(t[o]||""):t[o]||"");return n?n.substring(1):""},parseUrl:function(t){t=t||"";var e=t.split("//"),n=e[0]||"",o=(e[1]||"").split("/")[0];return{protocol:n,host:o}},ifIE:function(t,e){var n=document.createElement("b");return n.innerHTML="<!--[if "+(e||"")+" IE "+(t||"")+"]><i></i><![endif]-->",1===n.getElementsByTagName("i").length},platform:function(t){if(t){for(var e,n,o,r=new RegExp("(android|iphone|ipod|ios|ipad|webkit|windows phone|micromessenger)","g");null!=(n=r.exec(Ct));)e=!0,o=n[1],t[o]&&t[o](),t.ios&&/iphone|ipod|ipad/.test(o)&&t.ios(),t.wp&&/windows phone/.test(o)&&t.wp(),t.weixin&&/micromessenger/.test(o)&&t.weixin();!e&&t.others&&t.others()}},isRealMob:function(){var t=!1;return this.platform({ios:function(){t=!0},android:function(){t=!0},wp:function(){t=!0}}),t},encodeBase64:function(t){var e,n,o,r,i,a,s,c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",l="",u=0;for(t=this._utf8_encode(t);u<t.length;)e=t.charCodeAt(u++),n=t.charCodeAt(u++),o=t.charCodeAt(u++),r=e>>2,i=(3&e)<<4|n>>4,a=(15&n)<<2|o>>6,s=63&o,isNaN(n)?a=s=64:isNaN(o)&&(s=64),l=l+c.charAt(r)+c.charAt(i)+c.charAt(a)+c.charAt(s);return l},_utf8_encode:function(t){t=t.replace(/\r\n/g,"\n");for(var e="",n=0;n<t.length;n++){var o=t.charCodeAt(n);o<128?e+=String.fromCharCode(o):o>127&&o<2048?(e+=String.fromCharCode(o>>6|192),e+=String.fromCharCode(63&o|128)):(e+=String.fromCharCode(o>>12|224),e+=String.fromCharCode(o>>6&63|128),e+=String.fromCharCode(63&o|128))}return e},aryFindKeys:function(t,e){if(!t&&!e)return!1;for(var o=0,r=e.length;o<r;o++){var i=e[o],a=document.querySelectorAll(i);for(m=0,n=a.length;m<n;m++)if(a[m]===t)return!0}return!1},assign:function(t,e){"use strict";if(null===t||t===o)throw new TypeError("Cannot convert undefined or null to object");for(var n=Object(t),r=1;r<arguments.length;r++){var i=arguments[r];if(null!==i&&i!==o)for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(n[a]=i[a])}return n},set:function(t,e,n){var o=e.substring(0,e.indexOf("."));o?(o in t||(t[o]={}),Dt.set(t[o],e.substring(e.indexOf(".")+1),n)):t[e]=n},get:function(t,e){var n=e.substring(0,e.indexOf("."));return n&&n in t?Dt.get(t[n],e.substring(e.indexOf(".")+1)):n?t[n]:t[e]}},Lt={_isReady:!1,_readyList:[],ready:function(t){if(this._isReady)return t&&t();if(!e.body)return t&&this._readyList.push(t),setTimeout(function(){Lt.ready()});var n=this._readyList;this._isReady=!0;for(var o=0,r=n.length;o<r;o++)n[o]();t&&t(),n.length=0},find:function(t,e){return(e||ht).querySelectorAll(t)||[]},createEl:function(t,n){"object"==typeof t&&(n=t,t=""),n=n||{};var o=e.createElement(t||"div"),r=n.css;delete n.css;for(var i in n)o[i]=n[i];return r&&Lt.css(o,r),o},removeEl:function(t,e){var n=this.find(t,e);Dt.each(n,function(t){t.parentNode.removeChild(t)})},hasClass:function(t,e){return RegExp("\\b"+(e||"")+"\\b").test(t||"")},css:function(e,n,r){if(e&&n)if("object"==typeof n)for(var i in n)if(i.indexOf("-")>-1){var a,s=i.split("-")[0],c=i.split("-")[1];a=s+c.substring(0,1).toUpperCase()+c.substring(1),e.style[a]=n[i]}else e.style[i]=n[i];else{if(r===o){if(r=(t.getComputedStyle?t.getComputedStyle(e,null):e.currentStyle)[n]||"","auto"===r)return 0;if("px"===r.slice(r.length-2)){var l=parseFloat(r);return isNaN(l)?r:l}return r}e.style[n]=r}},css3:function(t){var e="";return Dt.each((t||"").split(";"),function(t){t&&(e+=t+";",Dt.each(["-webkit-","-moz-","-ms-","-o-"],function(n){e+=n+t+";"}))}),e},cssJoin:function(t,e){var n={};if(t){e=(e||"").split(",");for(var o=0,r=e.length;o<r;o++)n[e[o]]=Lt.css(t,e[o])}return n},outerWidth:function(t){var e=0,n=Lt.cssJoin(t,"width,paddingLeft,paddingRight,borderLeft,borderRight");for(var o in n)e+=parseFloat(n[o])||0;return e},outerHeight:function(t){var e=0,n=Lt.cssJoin(t,"height,paddingTop,paddingBottom,borderTop,borderBottom");for(var o in n)e+=parseFloat(n[o])||0;return e},getEvent:function(e){return e||t.event},on:function(t,e,n){t&&(t.addEventListener?t.addEventListener(e,n,!1):t.attachEvent("on"+e,n))},un:function(t,e,n){t&&(t.removeEventListener?t.removeEventListener(e,n,!1):t.detachEvent("on"+e,n))},stopPropagation:function(t){t=this.getEvent(t),t.stopPropagation?t.stopPropagation():t.cancelBubble=!0},getOffset:function(t){for(var e=0,n=0;t&&t!=ft;)e+=t.offsetLeft,n+=t.offsetTop,t=t.offsetParent;return{top:n,left:e}},formatSize:function(t){return t&&parseFloat(t)==t?t+"px":t},hide:function(t){t&&this.css(t,"display","none")},show:function(t,e){e=e||"block",t&&this.css(t,"display",e)},text:function(t,e){if(t)return e===o?"string"==typeof t.textContent?t.textContent:t.innerText:void("string"==typeof t.textContent?t.textContent=e:t.innerText=e)},html:function(t,e){t&&e&&(t.innerHTML=e)},toCssText:function(t){var e=document.createElement("div");return Lt.css(e,t),e.style.cssText}},Pt=function(t){if(t=t||{},t.html){t.type=t.type||"pop";var e,n,o,r,i,a,s=St+t.type+"_dialog",c=Dt.extend({},Z.pop),l="pop"===t.type,u=d(),p="position:fixed;z-index:"+ ++Ot+";font-size:14px;word-break:break-all;word-wrap:break-word;line-height:20px;"+Lt.css3("box-sizing:border-box;box-shadow:1px 1px 8px #eee"),f={pop:['<div class="{btnChatClz}" style="{commonStyle}border:1px solid #eee;padding:16px 30px 16px 16px;color:#888;cursor:pointer;background-color:#fff;text-align:left;border-radius:4px;box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.25);'+Lt.css3("transition: margin linear .3s")+'">','<div style="display: flex">','    <img src="{avatar}" style="flex: 0 0 32px;height: 32px;margin-right: 10px;'+Lt.css3("border-radius:16px")+'">',"<div>",'    <b style="font-size:16px;color:#666;margin-bottom: 6px">{title}</b>','    <div style="overflow: hidden;-webkit-line-clamp: 3;text-overflow:ellipsis;display: -webkit-box;-webkit-box-orient: vertical;word-break:normal">{html}</div>',"</div>",'    <a class="{btnCloseClz}" style="position:absolute;right:12px;top:12px;font-size:24px;cursor:pointer;color:rgba(0, 0, 0, 0.45);text-decoration:none;">&times;</a>',"</div>","</div>"].join(""),frame:['<div style="{commonStyle}top:50%;left:50%;background-color:{frame_color};width:450px;text-align:left;border-radius:4px;box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.25);">','    <a class="{btnCloseClz}" style="position:absolute;right:9px;top:8px;font-size:22px;cursor:pointer;color:#fff;text-decoration:none;">&times;</a>','    <div style="padding:9px 14px 9px 9px;color:#fff;font-size: 14px;line-height: 22px;display:flex;align-items: center"><img style="width: 20px" src="'+(Dt.isTestENV()?At+("//static-ud."+location.href+".tryudesk.com/img/client-invite.png"):At+"//static-ud.udesk.cn/img/client-invite.png")+'" />{title}</div>','    <div style="font-size:13px;padding:24px '+(c.is_custom_bg&&c.bg_url?"106px":0)+" 24px 16px;background:#fff"+(c.is_custom_bg?" url({bg_url})":"")+' no-repeat right bottom;background-size:auto 136px;">','        <div style="color:{color};min-height:'+(c.is_custom_bg&&c.bg_url?"60px":0)+';line-height:22px;margin-bottom:20px;">{html}</div>','        <a class="{btnChatClz}" style="display:inline-block;background-color:{btn_color};color:#fff;padding:5px 16px;cursor:pointer;text-decoration:none;box-shadow: 0px 2px 0px rgba(0, 0, 0, 0.03);border-radius:2px;">'+qt.g("tips.beganSession")+"</a>","    </div>","</div>"].join(""),mob_frame:['<div style="{commonStyle}top:50%;left:50%;width:280px;text-align:center;background-color:#fff;border:1px solid #ccc;">','    <div style="color:#333;padding:10px;">','        <img src="{avatar}" style="width:24px;height:24px;'+Lt.css3("border-radius:12px")+'">',"        <b>{title}</b>","    </div>",'    <div style="color:#333;padding:25px;border-top:1px solid #ccc;border-bottom:1px solid #ccc;">',"        {html}","    </div>",'    <div style="height:50px;line-height:50px;">','        <a class="{btnCloseClz}" style="float:left;width:49%;color:#307AE8;text-decoration:none;text-align:center;border-right:1px solid #ccc;">'+qt.g("tips.cancel")+"</a>",'        <a class="{btnChatClz}" style="float:right;width:49%;color:#307AE8;text-decoration:none;text-align:center;">'+qt.g("tips.beganSession")+"</a>","    </div>","</div>"].join("")},h=Dt.extend({commonStyle:p,btnCloseClz:St+"btn_close",btnChatClz:St+"btn_chat"},c,t);h.avatar=t.avatar||Rt.avatar||"",e=Dt.replaceTpl(f[t.type],h);var m=Z.pos_flag||"",g=m[0],y=Dt.extend({width:H?Lt.outerWidth(dt):Lt.css(dt,"width"),height:H?Lt.outerHeight(dt):Lt.css(dt,"height")},Lt.getOffset(dt)),v=function(){var t=Dt.extend({top:y.top,left:y.left,width:H?Lt.outerWidth(o):Lt.css(o,"width"),height:H?Lt.outerHeight(o):Lt.css(o,"height")}),e=Z.pop.offset||{},n=parseFloat(e.top)||0,r=parseFloat(e.left)||0,i=1,a="unset";Z.targetSelector&&Lt.css(o,{top:t.top+"px",left:it?"0px":t.left+"px"}),Dt.ifIE(8)&&(t.width=Math.max(t.width,320),t.height=Math.max(t.height,70)),u.top&&(n-="v"===g?20:y.height,a="translateY(-100%)"),u.bottom&&(n+="v"===g?y.height:20),u.left&&(r-=20,a="translateX(-100%) translateY(-50%)"),u.right&&(a="translateY(-50%)",r+=y.width+10,i=y.left+y.width+10),u.top||u.bottom?(r-=(t.width-y.width)/2,y.left<t.width?r=(parseFloat(e.left)||0)-10:yt-y.left<t.width&&(r=yt-y.left-t.width+(parseFloat(e.left)||0),r-=20)):n+=y.height/2-20,setTimeout(function(){Lt.css(o,{marginTop:n+"px",marginLeft:(it?i||r:r)+"px",marginRight:"10px",transform:a})},50)},b=function(){var t,e,n,r,i,a=Z.pop.arrow||{},s={},c={};r=Lt.formatSize(a.left)||"50%",it&&(r=y.left+(y.width+36)/2-20+"px"),i=Lt.formatSize(a.top)||"50%",u.top||u.bottom?(t=u.top?"bottom":"top",e="border"+(u.top?"Bottom":"Top")+"Width",n="border"+(u.top?"Top":"Bottom")+"Color",s={left:r,marginLeft:"-11px"},c={marginLeft:"-9px"}):(t=u.left?"right":"left",e="border"+(u.left?"Right":"Left")+"Width",n="border"+(u.left?"Left":"Right")+"Color",s={top:i,marginTop:"-11px"},c={marginTop:"-9px"}),s[t]="-12px",s[e]=0,s[n]="rgba(0,0,0,0.08)",c[t]="3px",c[e]=0,c[n]="#fff";var l=Lt.createEl("b",{css:{position:"absolute",border:"12px solid transparent"}}),p=Lt.createEl("b",{css:{position:"absolute",border:"9px solid transparent"}});Lt.css(l,s),Lt.css(p,c),l.appendChild(p),o.appendChild(l)},_=function(){var t=Lt.cssJoin(o,"width,height");Lt.css(o,{marginTop:-(t.height/2)+"px",marginLeft:-(t.width/2)+"px"})};Lt.removeEl("#"+s),n=Lt.createEl({id:s,innerHTML:e}),!Z.targetSelector&&(n.style.position="absolute"),o=n.firstChild,r=Lt.find("."+h.btnCloseClz,o)[0],i=l?o:Lt.find("."+h.btnChatClz,o)[0],l?(it&&(u.left?a=y.left-10+"px":u.right&&(a=yt-y.width-y.left-30+"px")),Lt.css(o,Dt.extend(it?{width:a?a:"100%"}:{"min-width":"250px","max-width":"300px"},c.css,/[tb]/.test(m)&&it?{left:0}:{}))):Lt.css(o,c.css);for(var w in y)"auto"===y[w]&&(y[w]=0);return Z.targetSelector?ht.appendChild(n):ht.getElementsByClassName("udesk-client-btn")[0].appendChild(n),i.onclick=t.onclick,r.onclick=function(e){Lt.stopPropagation(e),Lt.hide(n),t.onclose&&t.onclose()},l?(v(),b()):_(),{show:function(){Lt.show(n)},hide:function(){Lt.hide(n)}}}},Ut={_unread:0,baseStyles:{wrapperStyle:["display:flex;align-items:center"],btn:["display:block;position:fixed;"+Lt.css3("box-sizing:border-box;"),"font-size:13px;color:#fff;text-align:center;cursor:pointer;text-decoration:none;","z-index:"+ ++Ot+";","width:fit-content;","border-radius:4px;"],icon:["display:block;float:left;width:20px;height:20px;"],iconSrc:[At+"//"+Dt.getCurrentStaticUrl()+"/img/client-btn.png"],line:["display:block;float:left;width:1px;height:100%;background-color:rgba(0, 0, 0, .08);","vertical-align:middle;","background-color:#000\\9;filter:alpha(opacity=10)\\9;"],txt:["display:block;float:left;","overflow-y:hidden; font-size: 14px; color: #fff;"],mob_txt:["display:block;float:none;height:40px;margin:0 10px;line-height:40px;","overflow-y:hidden; font-size: 13px; color: #fff;"],circle:["display:none;position:absolute;top:-13px;right:-13px;height:22px;","text-align:center;line-height:20px;font-size:14px;color:#fff;border-radius:10px;","background-color: #FB5127;padding:0 6px;font-weight:bold;border:1px solid #fff;"],img_circle:["display:none;position:absolute;top:-13px;right:-13px;width:26px;height:26px;","text-align:center;line-height:26px;font-size:14px;color:#fff;border-radius:15px;","background-color: #ff3b30;"],visitor_no:["position: fixed;z-index: "+Ot+";color: #fff;text-align: center;font-family: 'Arial Normal', 'Arial';font-size: 13px;"],visitor_no_num:[]},abjuctStyle:function(){var t=this.baseStyles,e=(Z.css||{},Z.pos_flag||""),n=e[0],o=e[1],r=e[2],i=Z.visitor_no.enable;switch("i"!==n&&(t.btn.push("box-shadow:0 <0></0> 14px 0 rgba(0, 0, 0, .16);padding:10px;"),Z.color&&t.btn.push("background-color:"+Z.color+";")),n){case"h":t.btn.push("height:40px;"),it&&t.btn.push("width:100%;"),it&&t.btn.push("padding:0px;"),it&&t.icon.push("margin:10px;");break;case"v":t.btn.push("width:40px;height:auto;padding:10px 0px;"),t.icon.push("margin:0 7px 10px;"),t.line.push("width:100%;height:1px;"),t.circle.push("left:-13px;right:unset"),t.txt.push("width:40px;height:auto;word-break:break-all;word-wrap:break-word;line-height:20px;letter-spacing:24px;overflow-y:auto;overflow-x:hidden;margin-top:10px;padding-left: 2px;margin-left:10px"),t.mob_txt.push("width:40px;height:auto;float:left;word-break:break-all;word-wrap:break-word;line-height:20px;letter-spacing:24px;overflow-y:auto;overflow-x:hidden;margin-top:10px;padding-left: 2px;"),t.wrapperStyle.push("flex-direction:column;");break;case"c":t.btn.push("width:40px;height:40px;border-radius:20px;"),t.line.push("display:none;"),t.txt.push("display:none;"),t.iconSrc.length=0,t.iconSrc.push(At+"//"+Dt.getCurrentStaticUrl()+"/img/client-btn.png");break;case"i":t.img_circle.push("left:-13px;top:-13px;right:auto;bottom:auto;")}switch(o){case"l":t.btn.push("left:0;");break;case"r":t.btn.push("right:0;")}switch(r){case"t":t.btn.push("top:0;");break;case"b":t.btn.push("bottom:"+(it&&i?"30px":"0")+";")}var a=Z.visitor_no.color,s=Z.visitor_no.pos_flag||"",c=s[0],l=s[1];if(a||(a=it?"#307AE8":"#649ae8"),t.visitor_no.push("background-color:"+a+";"),it)t.visitor_no.push("width:100%;bottom:0;font-weight:bold;padding:5px 0;");else{switch(t.visitor_no.push("width: 70px;height: 70px;line-height: 22px;border-radius: 2px;"),t.visitor_no_num.push("font-size: 20px;margin-top: 14px;display: block;"),c){case"l":t.visitor_no.push("left:0;");break;case"r":t.visitor_no.push("right:0;")}switch(l){case"t":t.visitor_no.push("top:0;");break;case"b":t.visitor_no.push("bottom:0;")}}},tryCenter:function(t){var e=Z.pos_flag||"",n=e[0],o=e[1],r=e[2],i=Z.visitor_no.pos_flag||"",a=i[0],s=i[1],c=function(e){var n,i=Lt.cssJoin(e,"width,height,margin-right");Lt.cssJoin(mt,"width,height");"m"===o&&Lt.css(t,"left",(yt-parseFloat(i.width))/2+"px"),"m"===r&&(n=(vt-parseFloat(i.height))/2,Lt.css(t,"top",n+"px")),"m"===a&&Lt.css(mt,"left",(yt-parseFloat(i.width))/2+"px"),"m"===s&&(n=(vt-parseFloat(i.height))/2,!Dt.isRealMob()&&Lt.css(mt,"top",n+"px"))};if(c(t),"i"===n){var l=Lt.find("img",t)[0];l.onload=function(){c(l)}}},genVisitorNoHtml:function(){var t=this.baseStyles,e="",n=qt.g("tips.visitorNo");return bt=bt||"",Z.im_guest_enable&&Z.visitor_no.enable&&(e=Dt.replaceTpl('<div id="{flag}visitor_no" style="{style}">{text}</div>',{flag:St,style:t.visitor_no.join(""),text:it?n+"&nbsp<span>"+bt:"</span><b style='"+t.visitor_no_num.join("")+"'><span>"+bt+"</span></b>"+n})),e},genHtml:function(){var t,e=this.baseStyles,n=Z.css||{},o=Z.pos_flag||"",r=o[0],i=Z.unread_button||{},a="";if(this.abjuctStyle(),"i"!==r)a=Dt.replaceTpl('<div style="{wrapperStyle}"><span style="{iconStyle}"><img src="{iconSrc}" style="width:100%;height:100%;"/></span><span style="{lineStyle}"></span><span id="{flag}btn_text" style="{txtStyle}">{text}</span><span id="{flag}btn_circle" style="{circleStyle}"></span></div>',{flag:St,text:"",iconStyle:e.icon.join(""),iconSrc:e.iconSrc.join(""),lineStyle:e.line.join(""),txtStyle:e[it?"mob_txt":"txt"].join(""),circleStyle:e.circle.join(""),wrapperStyle:""});else if(t='<img class="buttonImg" src="'+(Z.offlineImgUrl||Z.onlineImgUrl)+'" style="max-width:'+(n.width?"unset;":"200px;"),n.width&&(t+="width: "+n.width+";"),n.height&&(t+="height: "+n.height+";"),t+='">',i.enable){var n=i.css||{};for(var s in n)n[s]?e.img_circle.push(s+":"+n[s]+"px;"):e.img_circle.push(s+":auto;");a=["<div>",t,'   <span id="'+St+'btn_circle" style="'+e.img_circle.join("")+'"></span>',"</div>"].join("")}else a=t;return a='<a class="udesk-client-btn" style="'+e.btn.join("")+'">'+a+"</a>",a+=this.genVisitorNoHtml()},updateVisitorNo:function(t){if(mt){var e=Lt.find("span",mt)[0];e.innerText=t}},clickHandler:function(e){try{var n=document.getElementsByClassName("udesk-client-btn")[0];if(n&&n.className.indexOf("drag")>-1)return}catch(o){}if("autoInvite"===e?A("autoInvite"):A(),t&&t.uAnalytics){var r=t.uAnalytics.user().id(),i=t.uAnalytics.user().traits(),a={system:"im",consult_url:t.location.href};Dt.assign(a,i.userAttr),t.uAnalytics.identify(r,a),t.uAnalytics.track("onlineconsultclick")}},clearUnread:function(t){if(this._unread){try{if(Mt.iframeEl&&Mt.iframeEl.contentWindow)Dt.sendMessage({type:"clearUnread",content:{clearHistory:t}},Mt.iframeEl.contentWindow);else{var e=Z.customer;$.post(G+"/spa1/sdk_offline_messages/customer_clear_message",{platform:"web",session_key:Z.session_key,web_token:e.web_token,nonce:e.nonce,signature:e.signature,timestamp:e.timestamp,encryption_algorithm:e.encryption_algorithm})}$.each(Ht,function(t,e){clearTimeout(e)}.bind(this))}catch(n){}this._unread=0,Lt.hide(this.circleEl),Z.noBubble||this.popObj&&this.popObj.hide()}},unreadHandler:function(t){if(!Mt.opened||!Mt.uiOpened){var e=this;if(t.count?this._unread=t.count:this._unread+=1,Lt.text(this.circleEl,this._unread),Lt.show(this.circleEl),Ft.execFn("onUnread",{count:this._unread}),!Z.noBubble&&t.msg){var n=function(){try{var t=document.getElementsByClassName("udesk-client-btn")[0];if(t&&t.className.indexOf("drag")>-1)return}catch(n){}e.clickHandler(),o&&o.hide(),e.popObj=!1},o=Pt({type:"pop",title:Rt.nick||qt.g("tips.agent"),avatar:Rt.avatar,html:t.msg||qt.g("tips.newMsg"),onclick:n,onclose:function(){e.popObj=!1,e.clearUnread(!0)}});this.popObj=o,dt.onclick=n}}},bindSelector:function(t,n){Lt.on(e,"click",function(e){e=e||window.event;for(var o=e.target,r=t.split(","),i=!1;o&&o!==this;){if(Dt.aryFindKeys(o,r)){i=!0;break}o=o.parentNode}i&&n&&n()})},init:function(){var t=this,n=Z.selector;n&&t.bindSelector(n,function(){A()});var o=Lt.find(Z.targetSelector,e)[0];if(Z.targetSelector&&!o&&t.bindSelector(Z.targetSelector,function(){t.clickHandler()}),!o){o=Lt.createEl({id:St+"btn",innerHTML:this.genHtml()}),ht.appendChild(o),o=Lt.find("a",o)[0],mt=Lt.find("#"+St+"visitor_no")[0],Ut.autoGen=!0,this.tryCenter(o),this.textEl=Lt.find("#"+St+"btn_text",o)[0],this.circleEl=Lt.find("#"+St+"btn_circle",o)[0],this.imgEl=Lt.find(".buttonImg",o)[0];var r={};try{if(Z.css)if("c"===Z.pos_flag[0]||"v"===Z.pos_flag[0])for(var i in Z.css)"width"!==i&&(r[i]=Z.css[i]);else r=Z.css}catch(a){r=Z.css}Lt.css(o,r),Lt.css(mt,Z.visitor_no.css)}o&&(this.el=o,dt=o,dt.onclick=this.clickHandler)}},Mt={opened:!1,uiOpened:!1,_baseStyles:{panel:["position:fixed;bottom:-574px;right:60px;z-index:-1;width:364px;height:572px;","overflow:hidden;display:none;background-color:transparent;","box-shadow:0px 4px 8px rgba(0, 0, 0, 0.25);","border-radius: 4px;","-webkit-overflow-scrolling:touch;","border:1px solid #ddd\\9;",Lt.css3("transition:bottom 0.3s;")],frame:["width:1px;min-width:100%;*width:100%;height:0;","border:none;padding:0;margin:0;float:none;background:none;",Lt.css3("transition:height 0.1s;")]},_styleElement:null,_getId:function(){return St+"panel"},_createPanel:function(t){var n=this,o=this._baseStyles||{},r=Lt.createEl({innerHTML:'<div id="'+this._getId()+'"><iframe id="'+St+'iframe" frameborder="0" scrolling="no" allowtransparency="true" style="'+o.frame.join("")+'"></iframe></div>'});ht.appendChild(r.firstChild),this.panelEl=e.getElementById(St+"panel"),this.iframeEl=e.getElementById(St+"iframe"),Lt.on(this.iframeEl,"load",function(){n.iframeLoaded=!0,t&&t()}),H&&!Dt.ifIE(8,"gt")||(this.iframeEl.setAttribute("allowfullscreen",""),this.iframeEl.setAttribute("mozallowfullscreen",""),this.iframeEl.setAttribute("webkitallowfullscreen","")),Lt.css(this.panelEl,Z.panel.css)},_doToggle:function(t){var e=this.panelEl;if(Lt.css(e,{display:t?"block":"none",visibility:t?"visible":"hidden",bottom:t?"-482px":0,zIndex:t?++Ot:-1}),setTimeout(function(){e.style.bottom=t?0:"-482px"},5),t)try{this.sendProduct(),Dt.sendMessage({type:"openPanel",preloadData:Bt},this.iframeEl.contentWindow)}catch(n){}},_uiCtrl:function(t){var e=this,n=this.iframeEl,o=Ut.el,r="hide"!==t;this.opened=r,this.uiOpened=r,r?e.iframeLoaded?e._doToggle(r):Lt.on(e.iframeEl,"load",function(){e.iframeLoaded=!0,e._doToggle(r)}):e._doToggle(r),Lt.show(r?n:o),Ut.autoGen&&Lt.hide(r?o:n),setTimeout(function(){n.style.height=r?"100%":"0"},100),Ft.execFn("panel.onToggle",{visible:r})},sendProduct:function(){for(var t=0;t<5;t++)setTimeout(Tt.setProduct,1e3*t)},show:function(t){var e;void 0===t&&(t=0);var n=this;if(this.iframeEl===o&&t<3)return void setTimeout(function(t){return function(){n.show(t)}}(t+1),100);var r=null==(e=this.iframeEl)?void 0:e.contentWindow;this._uiCtrl("show"),Ut.clearUnread(),Dt.sessionStore("panel_visible_"+Z.session_key,!0);try{this.sendProduct(),Dt.sendMessage({type:"forceLoadPage"},r),Dt.sendMessage({type:"openPanel"},r)}catch(i){}this.start_hide_time&&new Date-n.start_hide_time>=6e5&&(setTimeout(function(){n.loadSrc()},50),n.start_hide_time=null),kt&&kt()},_doToggleOther:function(t){var e=this.panelEl;if(Lt.css(e,{display:"none"}),t)try{this.sendProduct(),Dt.sendMessage({type:"openPanel",preloadData:Bt},this.iframeEl.contentWindow)}catch(n){}},_uiCtrlOther:function(t){var e=this,n=this.iframeEl,o=Ut.el,r="hide"!==t;this.opened=r,this.uiOpened=!1,r?e.iframeLoaded?e._doToggleOther(this.uiOpened):Lt.on(e.iframeEl,"load",function(){e.iframeLoaded=!0,e._doToggle(this.uiOpened)}):e._doToggle(this.uiOpened),Lt.show(o),Ut.autoGen&&Lt.hide(n),setTimeout(function(){n.style.height="0"},100)},_show:function(){var t=this,e=this.iframeEl.contentWindow;this._uiCtrlOther("show");try{this.sendProduct(),Dt.sendMessage({type:"forceLoadPage"},e),Dt.sendMessage({type:"openPanel"},e)}catch(n){}this.start_hide_time&&new Date-t.start_hide_time>=6e5&&(setTimeout(function(){t.loadSrc()},50),t.start_hide_time=null),kt&&kt()},hide:function(){"inner"===Z.mode&&(Dt.sessionStore("panel_visible_"+Z.session_key,""),this._uiCtrl("hide"),this.start_hide_time=new Date,kt=s())},loadSrc:function(t){var e=this;this.iframeEl&&p(function(n){e.iframeEl.contentWindow.location.replace(f(t,!1,n))})},_createStyle:function(){if(!this._styleElement){var t,e=this._baseStyles||{},n=It.config.window.proportion?100-It.config.window.proportion:30;t=It.config.window.proportion?"left:0;right:0;bottom:0;top:"+n+"%;height:auto;width:auto;":"left:0;right:0;bottom:0;top:"+n+"px;height:auto;width:auto;",this._styleElement=r("#"+this._getId()+"{"+e.panel.join("")+"}\n@media (max-width: 768px) {#"+this._getId()+"{"+t+"}}")}},init:function(){"inner"===Z.mode&&(this._createStyle(),this._createPanel(function(){var t=Dt.sessionStore("panel_visible_"+Z.session_key),e=Dt.sessionStore("panel_chatting_"+Z.session_key);t&&!it?A():t||it||e&&D(function(t){t&&N("hideIframe")})}))}},zt={rejected_num:0,agetn_invite:!1,init:function(){var t=this;this._loaded||(this._loaded=!0,rt=__udeskIo(Y+"?key="+Q,{reconnection:!0,reconnectionDelay:2e3,reconnectionAttempts:10}),rt.on("connect",function(){rt.emit("enter",{key:Q,code:Z.code})}),rt.on("reconnect",function(){rt.emit("enter",{key:Q,code:Z.code})}),rt.on("invite",function(e){e&&(Rt.id=e.agent_id,Rt.group_id=e.group_id),t.stopAutoStrategy(),t.agent_invite=!0,t.show()}),rt.on("robot_invite",function(t){t&&(Rt.id=t.agent_id,Rt.group_id=t.group_id);var e=T();e.removeEventListener("acceptedTransferAgent"),e.removeEventListener("rejectedTransferAgent"),e.addEventListener("acceptedTransferAgent",function(){rt.emit("accepted")}),e.addEventListener("rejectedTransferAgent",function(){rt.emit("rejected")}),Dt.sendMessage({type:"transferAgent",agentId:t.agent_id
},e)}),rt.on("force_chat",function(t){var e=t.agent_id;t&&(Z.agent_id=e,Z.chat_creator="agent",Rt.id=e);var n={agent_id:e,_timer:+new Date};n=JSON.stringify(n)||"",Dt.store("force_chat",n),Dt.store("on_force_chat")||(Dt.store("on_force_chat",+new Date),A("force_chat"))}))},show:function(t){if(t=t||{},dt){var e,n=this,o=Z.pop,r=function(){var t=document.getElementsByClassName("udesk-client-btn")[0];if(!(t&&t.className.indexOf("drag")>-1)){if(e)return void Ut.clickHandler();e=!0,ut=!0,pt&&(ut=!1),rt.emit("accepted"),n.dialog.hide(),Ut.clickHandler()}},i=function(){t.before&&t.before(),n.dialog?n.dialog.show():n.dialog=Pt({type:"frame"===o.type?it?"mob_frame":"frame":"pop",title:o.title||qt.g("tips.hello"),html:o.text||qt.g("tips.onlineContact"),onclick:r,onclose:function(){n.agent_invite&&(rt.emit("rejected"),ut=!1,n.agent_invite=!1,pt=!0),t.close&&t.close()}}),t.after&&t.after()};i(),dt.onclick=r}},inWorkTime:function(t){$.get(G+"/spa1/schedules/confirm_send_auto_invite",{id:V,current_time:+new Date}).always(function(e){1e3==e.code&&e.result&&t&&t()})},startAutoStrategy:function(){var t=this,e=Z.pop;if(e.auto_invite&&rt){var n=function(){t.show({isAutoStrategy:!0,close:function(){Dt.sessionStore("rejected_num")&&(t.rejected_num=Dt.sessionStore("rejected_num")),t.rejected_num++,Dt.sessionStore("rejected_num",t.rejected_num),t.rejected_num>=parseInt(e.reject_num)?t.stopAutoStrategy():o(),clearTimeout(t._autoRecvTimeout)}})},o=function(){e.interval&&(clearInterval(t._showDialogInterval),t._showDialogInterval=setInterval(function(){n()},1e3*parseFloat(e.interval)))};setTimeout(function(){t._stopAuto||Dt.sessionStore("rejected_num")>=parseInt(e.reject_num)||(n(),o(),e.auto_recv&&e.auto_recv_interval&&(t._autoRecvTimeout=setTimeout(function(){w()},1e3*parseFloat(e.auto_recv_interval))))},1e3*(parseFloat(e.delay)||0))}},stopAutoStrategy:function(){this._stopAuto=!0,clearTimeout(this._autoRecvTimeout),clearInterval(this._showDialogInterval),this.dialog&&this.dialog.hide()},sendChattingToAgent:function(){rt&&rt.emit("chatting")}},Ft={list:[],pushFn:function(t,e){!t||0!==t.indexOf("on")&&"panel.onToggle"!==t||"function"!=typeof e||this.list.push({key:t,fn:e})},off:function(t,e){for(var n=this.list.length-1;n>-1;n--){var o=this.list[n];t===o.key&&e===o.fn&&this.list.splice(n,1)}},eachPushFn:function(t){for(var e in t)if("object"==typeof t[e])for(var n in t[e])this.pushFn(e+"."+n,t[e][n]);else this.pushFn(e,t[e])},execFn:function(t,e){for(var n,o=this.list,r=0,i=o.length;r<i;r++)n=o[r],t===n.key&&n.fn(e)},init:function(){this._isloaded||(this._isloaded=!0,Dt.messageListener(function(t){switch(t.type){case"hidePanel":Mt.hide();break;case"newMsg":t.msg&&t.msg.search("emoji")>-1&&(t.msg=t.msg.replace(/\[emoji([^\].]+)\]/g,'<span class="udesk_im_client_emoji udesk_im_client_emoji$1"></span>')),Ut.unreadHandler(t),a();break;case"clearUnread":Ut.clearUnread();break;case"agent":t.src&&(Rt.avatar=t.src),Rt.nick=t.nick||"",wt=!0;break;case"chatDone":at=!1,Z.chat_creator="customer",ut=!1,Dt.sessionStore("panel_chatting_"+Z.session_key,null),kt&&kt();break;case"chatting":at=!0,lt=!1,zt.sendChattingToAgent(),Mt.start_hide_time=null,Dt.sessionStore("panel_chatting_"+Z.session_key,!0),kt=s();break;case"transfer":Mt.sendProduct();break;case"askPanelState":try{lt?Dt.sendMessage({type:"queuing",agent_id:Rt.id,chat_creator:Z.chat_creator||"customer"},Mt.iframeEl.contentWindow):Mt.opened&&(lt=!1,Dt.sendMessage({type:"chatting",agent_id:Rt.id,chat_creator:Z.chat_creator||"customer",invited:ut,reload:!0},Mt.iframeEl.contentWindow),Mt.opened&&Mt.uiOpened&&(ct=!0))}catch(n){}break;case"askReferrer":break;case"queuing":lt=!0;break;case"innerInputBlur":var o,r=1;try{setTimeout(function(){o=e.documentElement.scrollTop||e.body.scrollTop,o-=r,window.scrollTo(0,o),o+=r,window.scrollTo(0,o)},100)}catch(n){}break;case"robot_chatting":rt.emit("robot_chatting",{key:Q,code:Z.code});break;case"sendMsg":Et=!0,kt&&kt()}}),Dt.storageListener(function(t){t=t||{},"UDESK_force_chat"===t.key}))}},qt={"en-us":{tips:{beganSession:"Start to chat",online:"Online Consulting",cancel:"Cancel",agent:"Agent",newMsg:"A new message！",hello:"Hello,",onlineContact:"Currently has the personnel of the service online, immediately click consulting",callAgent:"Contact the customer service,consult online",leaveMsg:"Customer service is not at work, please leave a message",visitorNo:"Visitor's ID",file:"File",image:"Image",newMsgTitle:"New message"}},"zh-cn":{tips:{beganSession:"开始咨询",online:"在线咨询",cancel:"取消",agent:"客服",newMsg:"您有一条新消息",hello:"您好,",onlineContact:"当前有客服人员在线，立刻点击咨询吧。",callAgent:"联系客服，在线咨询",leaveMsg:"客服下班，请留言",visitorNo:"访客ID",file:"文件",image:"图片",newMsgTitle:"新消息"}},g:function(t,e){var n=Z.language||"zh-cn",o=this[n]||this["zh-cn"];t=t||"";for(var r=t.split("."),i=o[r[0]],a=1;a<r.length;a++)"object"==typeof i&&(i=i[r[a]]||"");return e&&i&&"object"!=typeof i&&(i=Dt.replaceTpl(i,e)),i||""}},Ht=[];z()}}(window,document);