(function(u){typeof define=="function"&&define.amd?define(u):u()})(function(){"use strict";function u(n){try{const t=document.getElementsByTagName("*"),e=[];for(;n&&n.nodeType===1;n=n.parentNode)if(n.hasAttribute("id")){let s=0;for(let i=0;i<t.length&&(t[i].hasAttribute("id")&&t[i].id===n.id&&s++,!(s>1));i++);return s===1?(e.unshift('//*[@id="'+n.getAttribute("id")+'"]'),e.join("/")):!1}else{let s=1;for(let i=n.previousSibling;i;i=i.previousSibling)i.localName===n.localName&&s++;s===1?n.nextElementSibling?n.nextElementSibling.localName!==n.localName?e.unshift(n.localName.toLowerCase()):e.unshift(n.localName.toLowerCase()+"["+s+"]"):e.unshift(n.localName.toLowerCase()):e.unshift(n.localName.toLowerCase()+"["+s+"]")}return e.length?"/"+e.join("/"):null}catch{return null}}function p(n,t){var i,o,a;let e="",s=[];if(n instanceof HTMLElement){if((n==null?void 0:n.parentElement)&&((i=n==null?void 0:n.parentElement)==null?void 0:i.className)&&(s=(o=n==null?void 0:n.parentElement)==null?void 0:o.className.split(" ")),s.includes(t))e=(a=n==null?void 0:n.parentElement)==null?void 0:a.className;else return p(n==null?void 0:n.parentElement,t);return e}}function w(n){const t=`.${n} {
      transition: all 0.5s;
      box-shadow: inset 0 0 10px 3px #17E900 !important;
      position: relative;
    }
    .hover-alert-guide9 {
      position: fixed;
      top: 0;
      left: 0;
      width: 230px;
      height: 44px;
      font-size: 16px;
      color: #fff;
      line-height: 44px;
      text-align: center;
      background: #1E1E1E;
      border-radius: 2px;
      z-index: 999999;
      display: none;
    }
    .hover-name {
      position: relative;
    }
    .hover-triangle {
      position: absolute;
      left: -7px;
      top: 50%;
      transform: translate(0, -50%);
      width: 0px;
      height: 0px;
      border-right: 7px solid #1E1E1E;
      border-top: 5px solid transparent;
      border-bottom: 5px solid transparent;
    }
    [data-guide-hover-id='current_opt_ele_click76'] {
      box-shadow: inset 0 0 10px 3px #17E900 !important;
      position: relative;
    }
    [data-guide-hover-id='current_opt_ele_click76']::before {
      content: attr(data-guide-hover-content) ;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      text-align: center;
      line-height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #17E900;
    }
    .voucher-cen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 99999;
    }`,e=document.createElement("style"),s=document.head||document.getElementsByTagName("head")[0];if(e.type="text/css",e.styleSheet){const i=function(){try{e.styleSheet.cssText=t}catch{}};e.styleSheet.disabled?setTimeout(i,10):i()}else{const i=document.createTextNode(t);e.appendChild(i)}s.appendChild(e)}function N(n,t){return!!n.className.split(" ").includes(t)}const S=["id","comments","glAccount","basePostedDr","basePostedCr","op"];function x(n){let t;if(!(n instanceof HTMLElement))return;const e=(n==null?void 0:n.parentElement)&&(n==null?void 0:n.parentElement.dataset.colField)||"";if(S.includes(e))t=n==null?void 0:n.parentElement;else return x(n==null?void 0:n.parentElement);return t}function g(n){const t=x(n);return t||""}const C=Math.floor(Math.random()*10),m="hover-alert-guide9";class E{constructor(t={}){this.options=Object.assign({name:"",select:!1,locationUrl:"",currentClassName:`current_opt_ele${C}`},t),this.states={isCircleDisabled:!1,isSelect:this.options.select,initDocument:null,hoverIndex:"",locationHash:"",xpathResultList:[]},this.options.select&&(this.states.isSelect=this.options.select),this.init(),w(this.options.currentClassName),this.openSelectCircle=(e="")=>{e!==""?this.states.isSelect=e:this.states.isSelect=!this.states.isSelect,this.states.isSelect||this.cleanDrawField()},this.cleanDrawFieldClick=(e="")=>{this.cleanDrawField("current_opt_ele_click76")},this.setIndex=e=>{this.states.hoverIndex=`\u6B65\u9AA4${e}`},this.handleIsRepeat=e=>{this.states.xpathResultList=e},this.guideSetXpathList=e=>{this.setXpathList(e)},this.clearAllXpath=()=>{this.states.xpathResultList.forEach(e=>{const s=document.evaluate(e,document,null,XPathResult.ANY_TYPE,null).iterateNext();s.dataset.guideHoverId="",s.dataset.guideHoverContent=""}),this.clearHoverAlert()},this.clearHoverAlert=()=>{const e=document.getElementsByClassName(m);e[0].style.display="none"}}init(){document.addEventListener("mouseover",t=>{var e,s;if(this.states.isSelect&&t.isTrusted!==!1){if((t.target.offsetWidth||t.target.clientWidth)>500||p(t.target,"AppMenu"))return;let o;const a=(s=(e=location.hash)==null?void 0:e.split("#/")[1])==null?void 0:s.split("?")[0];if(this.states.locationHash=a,this.options.singleUrl.includes(a)){const l=p(t.target,"voucher-main"),d=g(t.target);l&&d?o=d:o=t.target}else o=t.target;const c=this.drawFieldRepeat(o);c?this.clearHoverAlert():this.drawField(o),this.genGuide(o,!c)}},!0),document.addEventListener("mouseout",t=>{this.states.isSelect&&t.isTrusted!==!1&&this.cleanDrawField()},!0),document.addEventListener("click",t=>{if(!this.states.isSelect)return;const e=p(t.target,"voucher-main"),s=g(t.target);let i=t.target;if(e&&(window.event?window.event.cancelBubble=!0:t.stopPropagation(),t.preventDefault?t.preventDefault():window.event.returnValue,s&&(i=s)),!!N(i,this.options.currentClassName)){if(t.isTrusted!==!1){this.states.isCircleDisabled=!0;const a=this.getNodeFieldInfo(i);this.options.onClickTo.bind(this)(a),setTimeout(()=>{this.states.xpathResultList.push(a.xpath),i.dataset.guideHoverId="current_opt_ele_click76",i.dataset.guideHoverContent=this.states.hoverIndex,this.states.isSelect=!1,this.clearHoverAlert()},100)}window.event?window.event.cancelBubble=!0:t.stopPropagation(),t.preventDefault?t.preventDefault():window.event.returnValue}},!0),this.getDocumentDom()}drawField(t){!t||setTimeout(()=>(this.cleanDrawField(),t.className+=" "+this.options.currentClassName,t),300)}getDocumentDom(){const t=document.createElement("div");t.className=m,document.body.appendChild(t)}genGuide(t,e=!0){const{width:s,height:i,top:o,left:a}=t.getBoundingClientRect(),c=document.getElementsByClassName(m);c[0].setAttribute("style",`
      display: block;
      top:${o+i-44-8}px;
      left: ${s+a+10}px;
    `);let l="";e?l='<div class="hover-name"><i class="hover-triangle"></i>\u9F20\u6807\u5355\u51FB\u6DFB\u52A0\u5F15\u5BFC</div>':l='<div class="hover-name"><i class="hover-triangle"></i>\u5DF2\u7ECF\u88AB\u9009\u4E2D\u4E86, \u8BF7\u9009\u62E9\u5176\u4ED6\u54E6\uFF5E</div>',c[0].innerHTML=l}drawFieldRepeat(t){const e=u(t);return!!this.states.xpathResultList.includes(e)}cleanDrawField(t=""){const e=t||this.options.currentClassName,s=new RegExp("\\s*"+e,"gi");Array.from(document.getElementsByClassName(e)).forEach(function(i){i.className=i.className.replace(s,""),i.className||i.removeAttribute("class")})}getNodeFieldInfo(t){return{xpath:u(t),locationHash:this.states.locationHash}}setXpathList(t){const e=document.evaluate(t.xpath,document,null,XPathResult.ANY_TYPE,null).iterateNext();this.states.xpathResultList.forEach(s=>{if(s===t.xpath)e.dataset.guideHoverId="current_opt_ele_click76",e.dataset.guideHoverContent=`\u6B65\u9AA4${t.index}`;else{const i=document.evaluate(s,document,null,XPathResult.ANY_TYPE,null).iterateNext();i.dataset.guideHoverId="",i.dataset.guideHoverContent=""}})}}class T{constructor(t={}){this.options=Object.assign({name:"",singleUrl:[]},t),this.initExportCircle()}initExportCircle(){const t=new E({name:"\u5708\u9009",select:!1,singleUrl:this.options.singleUrl,onClickTo(e){window.parent.postMessage(e,"*")}});window.addEventListener("message",e=>{if(e.source!==window.parent)return;const s=e.data;if(s.locationHash&&(location.hash=s.locationHash),!t)throw"initSelectCircle \u672A\u521D\u59CB\u5316";s.isSelectCircle&&t.openSelectCircle(),s.index&&t.setIndex(s.index),s.xpath&&s.index&&t.guideSetXpathList(s),s.isClear&&(t.clearAllXpath(),t.openSelectCircle(!1)),s.guideListXpath&&t.handleIsRepeat(s.guideListXpath)})}}class f{constructor(t={}){this.options=Object.assign({msg:"",duration:3e3,timeout:null,resolve:null,isShow:!0},t),this.elements={spanTemp:null},this.getDuration(),this.addStyle()}getDuration(){this.initElement(),this.options.timeout=setTimeout(()=>{this.options.isShow=!1,this.getIsShow()},this.options.duration)}initElement(){const t=document.createElement("div");t.className="npm-com-toast32";const e=`
    <div class="mask-toast32">
      <div class="box-toast32">
        <div class="html-toast32">${this.options.msg}</div>
      </div>
    </div>`;t.innerHTML=e,this.elements.spanTemp=t,window.document.body.appendChild(t),this.closed();const s=document.getElementsByClassName("box-toast32")[0];s.onclick=i=>{i.stopPropagation()}}getIsShow(){this.options.isShow?this.elements.spanTemp.style.display="block":this.elements.spanTemp.style.display="none"}closed(){this.options.isShow=!this.options.isShow,this.elements.spanTemp.onclick=()=>{this.getIsShow(),clearTimeout(this.options.timeout),this.options.timeout=null}}addStyle(){const t=`.npm-com-toast32 {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: table;
      z-index: 999999999;
      line-height: 1;
    }
    .npm-com-toast32 .mask-toast32 {
      display: table-cell;
      vertical-align: middle;
      text-align: center;
      animation: npm-com-toast32-mask-animation 0.3s forwards;
    }
    .npm-com-toast32 .mask-toast32 .box-toast32 {
      display: inline-block;
      box-sizing: border-box;
      padding: 10px 15px;
      min-width: 120px;
      max-width: 200px;
      min-height: 40px;
      border-radius: 4px;
      background: rgba(0, 0, 0, 0.65);
      color: #FFF;
      font-size: 14px;
      line-height: 20px;
      animation: npm-com-toast32-mask-box-animation 0.3s forwards;
    }
    .npm-com-toast32 .mask-toast32 .box-toast32 .html-toast32 {
      display: inline-block;
      text-align: center;
    }
    @keyframes npm-com-toast32-mask-box-animation {
      0% {
        transform: scale(0);
      }

      100% {
        transform: scale(1);
      }
    }
    @keyframes npm-com-toast32-mask-animation {
      0% {
        background: rgba(0, 0, 0, 0);
      }

      100% {
        background: rgba(0, 0, 0, 0.1);
      }
    }`,e=document.createElement("style"),s=document.head||document.getElementsByTagName("head")[0];if(e.type="text/css",e.styleSheet){const i=function(){try{e.styleSheet.cssText=t}catch{}};e.styleSheet.disabled?setTimeout(i,10):i()}else{const i=document.createTextNode(t);e.appendChild(i)}s.appendChild(e)}}class k{constructor(t={}){var e,s;this.options=Object.assign({name:""},t),this.states={shepherd:null,guideList:[],module:"",func:"",guideValList:[],locationHref:(s=(e=location.hash)==null?void 0:e.split("#/")[1])==null?void 0:s.split("?")[0],lastType:"",btnClickDom:null,questionId:""},this.getGuideData=i=>{this.states.guideList=i.guideListData||[],this.states.module=i.module,this.states.func=i.func,this.states.questionId=i.questionId,this.initShepherd()},this.setStartGuideOpen=i=>{var o,a;if(i){if(console.log(this.states.shepherd.isActive(),"this.states.shepherd"),!this.states.questionId){this.states.shepherd.isActive()?this.states.shepherd.cancel():this.states.shepherd.start();return}this.states.questionId&&(location.hash=this.states.func,setTimeout(()=>{this.setGuideDataList(),this.states.questionId="",this.states.shepherd.start()},1500))}else(a=(o=this.states)==null?void 0:o.shepherd)==null||a.cancel()},this.addStyle()}initShepherd(){this.states.shepherd=new Shepherd.Tour({defaultStepOptions:{cancelIcon:{enabled:!1},classes:"custom-class-16",scrollTo:{behavior:"smooth",block:"center"}},steps:[],useModalOverlay:!0})}setGuideDataList(){if(this.states.guideList.length===0)return;const t=this.states.guideList||[],e=this.states.guideList.length;if(this.states.guideList.length===1){const l=this.getStepItem(t[0],e);this.states.guideValList=[l];return}const s=this.getStepItem(t[0],e),i=t.slice(1,t.length-1),o=[];i.forEach((l,d)=>{if(l.checkMethod==="1"){const r=i[d+1];r&&r.id&&(r.isNoneStep=!0)}const h=this.getStepItem(l,e,"center");o.push(h)});const a=t.length-1,c=this.getStepItem(t[a],e,"last");this.states.guideValList=[s,...o,c],this.states.shepherd.addSteps(this.states.guideValList)}getStepItem(t,e,s=""){const i=this,o={title:`<p class="guide-title">\u6B65\u9AA4${t.guideSort+1}</p>`,text:`<div class=text-guide"><p>${t.content}</p><span class="guide-num-module">${t.guideSort+1} / ${this.states.guideList.length}</span></div>`,classes:"shepherd-class-btn76",buttons:[{action:()=>{var h,r;(r=(h=this.states)==null?void 0:h.shepherd)==null||r.cancel()},classes:"shepherd-class-btn-item76",secondary:!0,text:"\u9000\u51FA"}],id:`step${t.guideSort+1}`,attachTo:{element:document.evaluate(t.xpath,document,null,XPathResult.ANY_TYPE,null).iterateNext(),on:"right"}},a={action:function(){return this.back()},classes:"shepherd-class-btn-item86",text:"\u4E0A\u4E00\u6B65"},c={action:()=>{var b,v;const h=(v=(b=i.states)==null?void 0:b.shepherd)==null?void 0:v.getCurrentStep().target.innerText,r=L(t,h);if(r===null)return this.next();if(t.checkMethod==="2"&&!r){new f({msg:"\u672A\u901A\u8FC7\u6821\u9A8C\uFF0C\u8BF7\u91CD\u65B0\u8F93\u5165\uFF5E",duration:3e3});return}this.states.shepherd.next(),s==="last"&&new f({msg:"\u606D\u559C\u60A8\uFF0C\u5DF2\u7ECF\u5B66\u4F1A\u4E86~",duration:3e3})},classes:"shepherd-class-btn-item86",text:"\u4E0B\u4E00\u6B65"},{isHaveNext:l,isHaveBack:d}=this.switchNext(t.checkMethod,s,e);return l&&!d&&o.buttons.push(c),l&&d&&!t.isNoneStep&&o.buttons.push(a,c),t.isNoneStep&&o.buttons.push(c),!l&&d&&o.buttons.push(a),this.handleStep(t,s),o}handleStep(t,e){var s;if(t.checkMethod==="1"){const i=document.evaluate(t.xpath,document,null,XPathResult.ANY_TYPE,null).iterateNext();this.states.btnClickDom=i,this.states.lastType=e,(s=this.states.btnClickDom)==null||s.addEventListener("click",this.handleClick(e),!1)}}handleClick(t=""){return()=>{var e,s,i,o;t==="last"?((s=(e=this.states)==null?void 0:e.shepherd)==null||s.cancel(),new f({msg:"\u606D\u559C\u60A8\uFF0C\u5DF2\u7ECF\u5B66\u4F1A\u4E86~",duration:3e3})):(o=(i=this.states)==null?void 0:i.shepherd)==null||o.next()}}switchNext(t,e,s){let i=!1,o=!1;switch(t){case"1":o=e==="center"||e==="last",i=!1;break;case"2":o=e==="center"||e==="last",i=!0;break;case"3":e==="center"||e==="last"?(o=!0,i=!0):s===1?i=!1:i=!0;break}return{isHaveNext:i,isHaveBack:o}}addStyle(){const t=`
    .custom-class-16 {
      margin-left: 16px !important;
    }
    .custom-class-16 .shepherd-content .shepherd-header {
      background: #fff !important;
      padding-bottom: 0 !important;
    }
    .guide-title {
      font-size: 18px !important;
      font-weight: bold !important;
      color: #232323 !important;
    }
    .custom-class-16 .shepherd-text {
      color: #717171 !important;
      font-size: 14px !important;
    }
    .shepherd-class-btn-item76 {
      background: #fff !important;
      color: #717171 !important;
    }
    .shepherd-class-btn-item86 {
      font-size: 14px !important;
      background: #fff !important;
      color: #232323 !important;
    }
    .text-guide {
      position: relative;
    }
    .text-guide p {
      font-size: 14px !important;
    }
    .guide-num-module {
      position: absolute;
      bottom: 20px;
      left: 15px;
      font-size: 14px;
    }`,e=document.createElement("style"),s=document.head||document.getElementsByTagName("head")[0];if(e.type="text/css",e.styleSheet){const i=function(){try{e.styleSheet.cssText=t}catch{}};e.styleSheet.disabled?setTimeout(i,10):i()}else{const i=document.createTextNode(t);e.appendChild(i)}s.appendChild(e)}}const L=(n,t)=>{const e=y(t);let s=!1;if(e.isNumber)n.checkValue===e.text&&(s=!0);else{const i=e.text,o=n.checkValue;i.search(o)>-1&&(s=!0)}return s},y=n=>{n=n.replace(/<\/?.+?>/g,""),n=n.replace(/[\r\n]/g,"");const t={text:"",isNumber:!1};return Number(n)>0?(t.text=n.replace(/^\s*/g,"").slice(0,-2),t.isNumber=!0):(t.text=n.split("\u4F59\u989D")[0],t.isNumber=!1),t};class I{constructor(t={}){this.options=Object.assign({name:"\u65B0\u624B\u5F15\u5BFC"},t),this.initNewGuide()}initNewGuide(){const t=new k({name:this.options.name});window.addEventListener("message",e=>{if(e.source!==window.parent)return;const s=e.data;s.guideListData&&s.module&&s.func&&t&&t.getGuideData(s),s&&t&&t.setStartGuideOpen(s.isOpenNewGuide)})}}new T({name:"\u5708\u9009",singleUrl:["hkj-voucher-new"]}),new I({name:"\u7528\u6237\u7AEF\u65B0\u624B\u5F15\u5BFC"})});
