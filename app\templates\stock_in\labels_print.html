<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>入库标签打印</title>
    <style>
        /* 打印设置 */
        @page {
            size: A4;
            margin: 10mm;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            font-size: 12px;
            line-height: 1.2;
            background: white;
        }

        /* A4纸上的标签网格布局 - 每行3个，每页15个标签 */
        .labels-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-gap: 5mm;
            width: 100%;
            max-width: 190mm; /* A4宽度减去边距 */
        }

        /* 单个标签样式 */
        .label {
            width: 60mm;
            height: 40mm;
            border: 2px solid #000;
            padding: 3mm;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            page-break-inside: avoid;
            background: white;
            position: relative;
        }

        /* 标签头部 - 食材名称 */
        .label-header {
            text-align: center;
            border-bottom: 1px solid #333;
            padding-bottom: 2mm;
            margin-bottom: 2mm;
        }

        .ingredient-name {
            font-size: 14px;
            font-weight: bold;
            color: #000;
            word-wrap: break-word;
            line-height: 1.1;
        }

        /* 标签主体信息 */
        .label-body {
            flex: 1;
            display: flex;
            justify-content: space-between;
        }

        .label-info {
            flex: 1;
            font-size: 13px;
            line-height: 1.3;
        }

        .label-info-row {
            margin-bottom: 1mm;
            display: flex;
            align-items: flex-start;
        }

        .label-info-label {
            font-weight: bold;
            min-width: 16mm;
            color: #333;
        }

        .label-info-value {
            flex: 1;
            word-wrap: break-word;
            color: #000;
        }

        /* 二维码区域 */
        .qr-code-container {
            width: 15mm;
            height: 15mm;
            margin-left: 2mm;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #ddd;
            flex-shrink: 0;
        }

        .qr-code {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .qr-placeholder {
            font-size: 8px;
            text-align: center;
            color: #666;
            line-height: 1.1;
        }

        /* 标签底部 */
        .label-footer {
            border-top: 1px solid #333;
            padding-top: 1mm;
            margin-top: 1mm;
            font-size: 8px;
            text-align: center;
            color: #666;
        }

        /* 分页控制 */
        .page-break {
            page-break-before: always;
        }

        /* 打印时隐藏不需要的元素 */
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }

        /* 打印按钮 */
        .print-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
            background: white;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .btn {
            padding: 8px 16px;
            margin: 0 5px;
            border: 1px solid #007bff;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            border-color: #6c757d;
        }

        .btn-secondary:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <!-- 打印控制按钮 -->
    <div class="print-controls no-print">
        <button onclick="window.print()" class="btn">
            <i class="fas fa-print"></i> 打印标签
        </button>
        <button onclick="window.close()" class="btn btn-secondary">
            <i class="fas fa-times"></i> 关闭
        </button>
    </div>

    <!-- 标签容器 -->
    <div class="labels-container">
        {% for item in items %}
        <!-- 每15个标签后分页 -->
        {% if loop.index0 > 0 and loop.index0 % 15 == 0 %}
        </div>
        <div class="page-break"></div>
        <div class="labels-container">
        {% endif %}
        
        <div class="label">
            <!-- 标签头部 -->
            <div class="label-header">
                <div class="ingredient-name">{{ item.ingredient.name }}</div>
            </div>

            <!-- 标签主体 -->
            <div class="label-body">
                <div class="label-info">
                    <div class="label-info-row">
                        <span class="label-info-label">批次:</span>
                        <span class="label-info-value">{{ item.batch_number }}</span>
                    </div>
                    <div class="label-info-row">
                        <span class="label-info-label">数量:</span>
                        <span class="label-info-value">{{ item.quantity }}{{ item.unit }}</span>
                    </div>
                    <div class="label-info-row">
                        <span class="label-info-label">供应商:</span>
                        <span class="label-info-value">{{ item.supplier.name if item.supplier else '-' }}</span>
                    </div>
                    {% if item.supplier and item.supplier.phone %}
                    <div class="label-info-row">
                        <span class="label-info-label">电话:</span>
                        <span class="label-info-value">{{ item.supplier.phone }}</span>
                    </div>
                    {% endif %}
                    {% if item.expiry_date %}
                    <div class="label-info-row">
                        <span class="label-info-label">过期:</span>
                        <span class="label-info-value">{{ item.expiry_date.strftime('%Y-%m-%d') if item.expiry_date else '-' }}</span>
                    </div>
                    {% endif %}
                </div>

                <!-- 二维码区域 -->
                <div class="qr-code-container">
                    {% if item.qr_code %}
                    <img src="data:image/png;base64,{{ item.qr_code }}" alt="QR Code" class="qr-code">
                    {% else %}
                    <div class="qr-placeholder">溯源<br>二维码</div>
                    {% endif %}
                </div>
            </div>

            <!-- 标签底部 -->
            <div class="label-footer">
                入库日期: {{ item.stock_in.stock_in_date.strftime('%Y-%m-%d') if item.stock_in.stock_in_date else '-' }}
            </div>
        </div>
        {% endfor %}
    </div>

    <script>
        // 自动打印（可选）
        // window.onload = function() {
        //     window.print();
        // };
        
        // 打印后关闭窗口
        window.onafterprint = function() {
            // window.close();
        };
    </script>
</body>
</html>
