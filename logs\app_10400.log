2025-06-19 22:14:14,086 INFO: 应用启动 - PID: 10400 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-19 22:16:19,974 ERROR: Exception on /financial/reports/payables-aging [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\reports.py", line 139, in payables_aging
    return render_template('financial/reports/payables_aging.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\financial\reports\payables_aging.html", line 1, in top-level template code
    {% extends "financial/base.html" %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 429, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 118, in block 'content'
    {% block financial_content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\financial\reports\payables_aging.html", line 121, in block 'financial_content'
    {% for item in group_data.items %}
TypeError: 'builtin_function_or_method' object is not iterable
2025-06-19 22:16:22,959 ERROR: Exception on /financial/reports/payables-aging [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\reports.py", line 139, in payables_aging
    return render_template('financial/reports/payables_aging.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\financial\reports\payables_aging.html", line 1, in top-level template code
    {% extends "financial/base.html" %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 429, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 118, in block 'content'
    {% block financial_content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\financial\reports\payables_aging.html", line 121, in block 'financial_content'
    {% for item in group_data.items %}
TypeError: 'builtin_function_or_method' object is not iterable
2025-06-19 22:18:13,598 ERROR: Exception on /financial/reports/payables-aging [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\reports.py", line 139, in payables_aging
    return render_template('financial/reports/payables_aging.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\financial\reports\payables_aging.html", line 1, in top-level template code
    {% extends "financial/base.html" %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 429, in top-level template code
    {% block content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 118, in block 'content'
    {% block financial_content %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\financial\reports\payables_aging.html", line 121, in block 'financial_content'
    {% for item in group_data['items'] %}
TypeError: 'builtin_function_or_method' object is not iterable
