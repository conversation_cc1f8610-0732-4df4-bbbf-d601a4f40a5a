2025-06-20 07:15:20,830 INFO: 应用启动 - PID: 4464 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-20 10:06:07,098 ERROR: Exception on /admin/ [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_admin\base.py", line 69, in inner
    return self._run_view(f, *args, **kwargs)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_admin\base.py", line 369, in _run_view
    return fn(self, *args, **kwargs)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_admin\base.py", line 455, in index
    return self.render(self._template)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_admin\base.py", line 308, in render
    return render_template(template, **kwargs)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_admin\templates\bootstrap4\admin\index.html", line 1, in top-level template code
    {% extends 'admin/master.html' %}
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_admin\templates\bootstrap4\admin\master.html", line 1, in top-level template code
    {% extends admin_base_template %}
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_admin\templates\bootstrap4\admin\base.html", line 6, in top-level template code
    <title>{% block title %}{% if admin_view.category %}{{ admin_view.category }} - {% endif %}{{ admin_view.name }} - {{ admin_view.admin.name }}{% endblock %}</title>
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_admin\templates\bootstrap4\admin\base.html", line 6, in block 'title'
    <title>{% block title %}{% if admin_view.category %}{{ admin_view.category }} - {% endif %}{{ admin_view.name }} - {{ admin_view.admin.name }}{% endblock %}</title>
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_babel\speaklater.py", line 66, in __html__
    return str(self)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_babel\speaklater.py", line 21, in __str__
    return str(self._func(*self._args, **self._kwargs))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_babel\__init__.py", line 648, in gettext
    t = self.get_translations()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_babel\__init__.py", line 609, in get_translations
    locale = get_locale()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_babel\__init__.py", line 259, in get_locale
    babel = get_babel()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_babel\__init__.py", line 46, in get_babel
    return app.extensions['babel']
KeyError: 'babel'
